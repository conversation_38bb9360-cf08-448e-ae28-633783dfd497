# n8n Configuration
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http

# Database Configuration
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=localhost
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n_trading
DB_POSTGRESDB_USER=n8n_user
DB_POSTGRESDB_PASSWORD=your_secure_password

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Binance API (Optional)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here

# CoinGecko API (Optional)
COINGECKO_API_KEY=your_coingecko_api_key_here

# System Configuration
LOG_LEVEL=info
NODE_ENV=production
TZ=UTC

# Trading Configuration
DEFAULT_RISK_LEVEL=medium
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=5
TAKE_PROFIT_PERCENTAGE=15

# Alert Configuration
ENABLE_TELEGRAM_ALERTS=true
ENABLE_EMAIL_ALERTS=false
ALERT_COOLDOWN_MINUTES=30

# Performance Configuration
API_TIMEOUT_MS=10000
RETRY_ATTEMPTS=3
RETRY_DELAY_MS=2000

# Security
WEBHOOK_SECRET=your_webhook_secret_here
API_RATE_LIMIT=100
