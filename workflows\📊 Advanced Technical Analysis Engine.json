{"name": "📊 Advanced Technical Analysis Engine", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 3}]}}, "id": "schedule-trigger", "name": "Every 3 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 300], "onError": "continueRegularOutput"}, {"parameters": {"httpMethod": "POST", "path": "technical-analysis-trigger", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "External Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [0, 180], "webhookId": "technical-analysis-trigger", "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT symbol FROM trading_symbols WHERE active = true AND technical_analysis_enabled = true ORDER BY priority DESC", "options": {}}, "id": "get-analysis-symbols", "name": "Get Analysis Symbols", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [200, 240], "credentials": {"postgres": {"id": "trading-postgres", "name": "Trading PostgreSQL"}}, "onError": "continueRegularOutput"}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-symbols", "name": "Process Symbols Individually", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [400, 240], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT bucket, symbol, open_price, high_price, low_price, close_price, volume FROM market_data_5min WHERE symbol = $1 AND bucket > NOW() - INTERVAL '24 hours' ORDER BY bucket ASC", "parameters": {"parameters": [{"name": "symbol", "value": "={{ $json.symbol }}USDT"}]}, "options": {}}, "id": "get-ohlcv-data", "name": "Get OHLCV Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [600, 240], "credentials": {"postgres": {"id": "trading-postgres", "name": "Trading PostgreSQL"}}, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Advanced Technical Indicators Calculator\nconst ohlcvData = $input.all();\nconst symbol = $('Process Symbols Individually').first().json.symbol;\n\nif (ohlcvData.length < 50) {\n  return [{\n    json: {\n      symbol: symbol,\n      error: 'INSUFFICIENT_DATA',\n      data_points: ohlcvData.length,\n      required_minimum: 50,\n      status: 'FAILED'\n    }\n  }];\n}\n\n// Sort data by timestamp\nconst sortedData = ohlcvData.map(d => d.json).sort((a, b) => new Date(a.bucket) - new Date(b.bucket));\n\n// Extract price arrays\nconst opens = sortedData.map(d => parseFloat(d.open_price));\nconst highs = sortedData.map(d => parseFloat(d.high_price));\nconst lows = sortedData.map(d => parseFloat(d.low_price));\nconst closes = sortedData.map(d => parseFloat(d.close_price));\nconst volumes = sortedData.map(d => parseFloat(d.volume));\n\n// Technical Indicators Library\nclass TechnicalIndicators {\n  static sma(values, period) {\n    if (values.length < period) return null;\n    const sum = values.slice(-period).reduce((a, b) => a + b, 0);\n    return sum / period;\n  }\n  \n  static ema(values, period) {\n    if (values.length < period) return null;\n    \n    const multiplier = 2 / (period + 1);\n    let ema = this.sma(values.slice(0, period), period);\n    \n    for (let i = period; i < values.length; i++) {\n      ema = (values[i] * multiplier) + (ema * (1 - multiplier));\n    }\n    return ema;\n  }\n  \n  static rsi(values, period = 14) {\n    if (values.length < period + 1) return null;\n    \n    let gains = 0, losses = 0;\n    \n    // Calculate initial average gain and loss\n    for (let i = 1; i <= period; i++) {\n      const change = values[i] - values[i - 1];\n      if (change > 0) gains += change;\n      else losses += Math.abs(change);\n    }\n    \n    let avgGain = gains / period;\n    let avgLoss = losses / period;\n    \n    // Calculate RSI for remaining periods\n    for (let i = period + 1; i < values.length; i++) {\n      const change = values[i] - values[i - 1];\n      const gain = change > 0 ? change : 0;\n      const loss = change < 0 ? Math.abs(change) : 0;\n      \n      avgGain = ((avgGain * (period - 1)) + gain) / period;\n      avgLoss = ((avgLoss * (period - 1)) + loss) / period;\n    }\n    \n    if (avgLoss === 0) return 100;\n    const rs = avgGain / avgLoss;\n    return 100 - (100 / (1 + rs));\n  }\n  \n  static macd(values, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {\n    const fastEMA = this.ema(values, fastPeriod);\n    const slowEMA = this.ema(values, slowPeriod);\n    \n    if (!fastEMA || !slowEMA) return null;\n    \n    const macdLine = fastEMA - slowEMA;\n    \n    // Calculate signal line (EMA of MACD line)\n    const macdHistory = [];\n    for (let i = slowPeriod - 1; i < values.length; i++) {\n      const fEMA = this.ema(values.slice(0, i + 1), fastPeriod);\n      const sEMA = this.ema(values.slice(0, i + 1), slowPeriod);\n      if (fEMA && sEMA) {\n        macdHistory.push(fEMA - sEMA);\n      }\n    }\n    \n    const signalLine = this.ema(macdHistory, signalPeriod);\n    const histogram = signalLine ? macdLine - signalLine : 0;\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram: histogram\n    };\n  }\n  \n  static bollingerBands(values, period = 20, stdDev = 2) {\n    if (values.length < period) return null;\n    \n    const sma = this.sma(values, period);\n    const recentValues = values.slice(-period);\n    \n    const variance = recentValues.reduce((sum, value) => {\n      return sum + Math.pow(value - sma, 2);\n    }, 0) / period;\n    \n    const standardDeviation = Math.sqrt(variance);\n    \n    return {\n      upper: sma + (standardDeviation * stdDev),\n      middle: sma,\n      lower: sma - (standardDeviation * stdDev),\n      bandwidth: (standardDeviation * stdDev * 2) / sma * 100\n    };\n  }\n  \n  static stochastic(highs, lows, closes, kPeriod = 14, dPeriod = 3) {\n    if (closes.length < kPeriod) return null;\n    \n    const recentHighs = highs.slice(-kPeriod);\n    const recentLows = lows.slice(-kPeriod);\n    const currentClose = closes[closes.length - 1];\n    \n    const highestHigh = Math.max(...recentHighs);\n    const lowestLow = Math.min(...recentLows);\n    \n    const kPercent = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;\n    \n    // Calculate %D (SMA of %K)\n    const kValues = [];\n    for (let i = kPeriod - 1; i < closes.length; i++) {\n      const periodHighs = highs.slice(i - kPeriod + 1, i + 1);\n      const periodLows = lows.slice(i - kPeriod + 1, i + 1);\n      const periodHigh = Math.max(...periodHighs);\n      const periodLow = Math.min(...periodLows);\n      \n      if (periodHigh !== periodLow) {\n        kValues.push(((closes[i] - periodLow) / (periodHigh - periodLow)) * 100);\n      }\n    }\n    \n    const dPercent = kValues.length >= dPeriod ? this.sma(kValues, dPeriod) : null;\n    \n    return {\n      k: kPercent,\n      d: dPercent\n    };\n  }\n  \n  static atr(highs, lows, closes, period = 14) {\n    if (closes.length < period + 1) return null;\n    \n    const trueRanges = [];\n    \n    for (let i = 1; i < closes.length; i++) {\n      const tr1 = highs[i] - lows[i];\n      const tr2 = Math.abs(highs[i] - closes[i - 1]);\n      const tr3 = Math.abs(lows[i] - closes[i - 1]);\n      \n      trueRanges.push(Math.max(tr1, tr2, tr3));\n    }\n    \n    return this.sma(trueRanges, period);\n  }\n  \n  static adx(highs, lows, closes, period = 14) {\n    if (closes.length < period * 2) return null;\n    \n    const dmPlus = [];\n    const dmMinus = [];\n    const trueRanges = [];\n    \n    // Calculate directional movements and true ranges\n    for (let i = 1; i < closes.length; i++) {\n      const highDiff = highs[i] - highs[i - 1];\n      const lowDiff = lows[i - 1] - lows[i];\n      \n      dmPlus.push(highDiff > lowDiff && highDiff > 0 ? highDiff : 0);\n      dmMinus.push(lowDiff > highDiff && lowDiff > 0 ? lowDiff : 0);\n      \n      const tr1 = highs[i] - lows[i];\n      const tr2 = Math.abs(highs[i] - closes[i - 1]);\n      const tr3 = Math.abs(lows[i] - closes[i - 1]);\n      trueRanges.push(Math.max(tr1, tr2, tr3));\n    }\n    \n    // Calculate smoothed averages\n    const smoothedDMPlus = this.sma(dmPlus, period);\n    const smoothedDMMinus = this.sma(dmMinus, period);\n    const smoothedTR = this.sma(trueRanges, period);\n    \n    if (!smoothedTR || smoothedTR === 0) return null;\n    \n    const diPlus = (smoothedDMPlus / smoothedTR) * 100;\n    const diMinus = (smoothedDMMinus / smoothedTR) * 100;\n    \n    const dx = Math.abs(diPlus - diMinus) / (diPlus + diMinus) * 100;\n    \n    return {\n      adx: dx,\n      diPlus: diPlus,\n      diMinus: diMinus\n    };\n  }\n}\n\n// Calculate all indicators\nconst currentPrice = closes[closes.length - 1];\nconst sma20 = TechnicalIndicators.sma(closes, 20);\nconst sma50 = TechnicalIndicators.sma(closes, 50);\nconst sma200 = TechnicalIndicators.sma(closes, 200);\nconst ema12 = TechnicalIndicators.ema(closes, 12);\nconst ema26 = TechnicalIndicators.ema(closes, 26);\nconst rsi = TechnicalIndicators.rsi(closes, 14);\nconst macd = TechnicalIndicators.macd(closes);\nconst bollinger = TechnicalIndicators.bollingerBands(closes);\nconst stoch = TechnicalIndicators.stochastic(highs, lows, closes);\nconst atr = TechnicalIndicators.atr(highs, lows, closes);\nconst adx = TechnicalIndicators.adx(highs, lows, closes);\n\n// Support and Resistance calculation\nconst recentData = sortedData.slice(-50);\nconst pivotPoints = [];\n\nfor (let i = 2; i < recentData.length - 2; i++) {\n  const current = parseFloat(recentData[i].high_price);\n  const prev2 = parseFloat(recentData[i - 2].high_price);\n  const prev1 = parseFloat(recentData[i - 1].high_price);\n  const next1 = parseFloat(recentData[i + 1].high_price);\n  const next2 = parseFloat(recentData[i + 2].high_price);\n  \n  // Resistance (local high)\n  if (current > prev2 && current > prev1 && current > next1 && current > next2) {\n    pivotPoints.push({ price: current, type: 'resistance' });\n  }\n  \n  const currentLow = parseFloat(recentData[i].low_price);\n  const prev2Low = parseFloat(recentData[i - 2].low_price);\n  const prev1Low = parseFloat(recentData[i - 1].low_price);\n  const next1Low = parseFloat(recentData[i + 1].low_price);\n  const next2Low = parseFloat(recentData[i + 2].low_price);\n  \n  // Support (local low)\n  if (currentLow < prev2Low && currentLow < prev1Low && currentLow < next1Low && currentLow < next2Low) {\n    pivotPoints.push({ price: currentLow, type: 'support' });\n  }\n}\n\nconst resistanceLevels = pivotPoints.filter(p => p.type === 'resistance').map(p => p.price);\nconst supportLevels = pivotPoints.filter(p => p.type === 'support').map(p => p.price);\n\nconst nearestResistance = resistanceLevels.filter(r => r > currentPrice).sort((a, b) => a - b)[0];\nconst nearestSupport = supportLevels.filter(s => s < currentPrice).sort((a, b) => b - a)[0];\n\n// Volume analysis\nconst avgVolume = TechnicalIndicators.sma(volumes, 20);\nconst currentVolume = volumes[volumes.length - 1];\nconst volumeRatio = avgVolume ? currentVolume / avgVolume : 1;\n\n// Volatility calculation\nconst returns = [];\nfor (let i = 1; i < closes.length; i++) {\n  returns.push((closes[i] - closes[i - 1]) / closes[i - 1]);\n}\nconst volatility = Math.sqrt(returns.reduce((sum, ret) => sum + ret * ret, 0) / returns.length) * Math.sqrt(288) * 100; // Annualized\n\n// Trend analysis\nlet trendDirection = 'SIDEWAYS';\nif (sma20 && sma50 && sma200) {\n  if (currentPrice > sma20 && sma20 > sma50 && sma50 > sma200) {\n    trendDirection = 'STRONG_BULLISH';\n  } else if (currentPrice > sma20 && sma20 > sma50) {\n    trendDirection = 'BULLISH';\n  } else if (currentPrice < sma20 && sma20 < sma50 && sma50 < sma200) {\n    trendDirection = 'STRONG_BEARISH';\n  } else if (currentPrice < sma20 && sma20 < sma50) {\n    trendDirection = 'BEARISH';\n  }\n}\n\n// Signal generation\nconst signals = [];\n\n// RSI signals\nif (rsi) {\n  if (rsi < 30) signals.push({ type: 'RSI_OVERSOLD', strength: 'STRONG', direction: 'BUY' });\n  else if (rsi > 70) signals.push({ type: 'RSI_OVERBOUGHT', strength: 'STRONG', direction: 'SELL' });\n  else if (rsi < 40) signals.push({ type: 'RSI_WEAK', strength: 'MEDIUM', direction: 'BUY' });\n  else if (rsi > 60) signals.push({ type: 'RSI_STRONG', strength: 'MEDIUM', direction: 'SELL' });\n}\n\n// MACD signals\nif (macd && macd.macd && macd.signal) {\n  if (macd.macd > macd.signal && macd.histogram > 0) {\n    signals.push({ type: 'MACD_BULLISH', strength: 'MEDIUM', direction: 'BUY' });\n  } else if (macd.macd < macd.signal && macd.histogram < 0) {\n    signals.push({ type: 'MACD_BEARISH', strength: 'MEDIUM', direction: 'SELL' });\n  }\n}\n\n// Bollinger Bands signals\nif (bollinger) {\n  if (currentPrice <= bollinger.lower) {\n    signals.push({ type: 'BB_OVERSOLD', strength: 'STRONG', direction: 'BUY' });\n  } else if (currentPrice >= bollinger.upper) {\n    signals.push({ type: 'BB_OVERBOUGHT', strength: 'STRONG', direction: 'SELL' });\n  }\n}\n\n// Stochastic signals\nif (stoch && stoch.k && stoch.d) {\n  if (stoch.k < 20 && stoch.d < 20) {\n    signals.push({ type: 'STOCH_OVERSOLD', strength: 'MEDIUM', direction: 'BUY' });\n  } else if (stoch.k > 80 && stoch.d > 80) {\n    signals.push({ type: 'STOCH_OVERBOUGHT', strength: 'MEDIUM', direction: 'SELL' });\n  }\n}\n\n// Calculate confidence score\nconst buySignals = signals.filter(s => s.direction === 'BUY');\nconst sellSignals = signals.filter(s => s.direction === 'SELL');\n\nlet overallSignal = 'HOLD';\nlet confidenceScore = 0.5;\n\nif (buySignals.length > sellSignals.length) {\n  overallSignal = 'BUY';\n  confidenceScore = Math.min(0.5 + (buySignals.length * 0.15), 0.95);\n} else if (sellSignals.length > buySignals.length) {\n  overallSignal = 'SELL';\n  confidenceScore = Math.min(0.5 + (sellSignals.length * 0.15), 0.95);\n}\n\n// Risk assessment\nlet riskLevel = 'MEDIUM';\nif (volatility > 50) riskLevel = 'HIGH';\nelse if (volatility < 20) riskLevel = 'LOW';\n\nconst result = {\n  symbol: symbol,\n  timestamp: new Date().toISOString(),\n  current_price: Math.round(currentPrice * 100000) / 100000,\n  \n  // Moving Averages\n  sma_20: sma20 ? Math.round(sma20 * 100000) / 100000 : null,\n  sma_50: sma50 ? Math.round(sma50 * 100000) / 100000 : null,\n  sma_200: sma200 ? Math.round(sma200 * 100000) / 100000 : null,\n  ema_12: ema12 ? Math.round(ema12 * 100000) / 100000 : null,\n  ema_26: ema26 ? Math.round(ema26 * 100000) / 100000 : null,\n  \n  // Oscillators\n  rsi: rsi ? Math.round(rsi * 100) / 100 : null,\n  macd_line: macd?.macd ? Math.round(macd.macd * 100000) / 100000 : null,\n  macd_signal: macd?.signal ? Math.round(macd.signal * 100000) / 100000 : null,\n  macd_histogram: macd?.histogram ? Math.round(macd.histogram * 100000) / 100000 : null,\n  \n  // Bollinger Bands\n  bb_upper: bollinger?.upper ? Math.round(bollinger.upper * 100000) / 100000 : null,\n  bb_middle: bollinger?.middle ? Math.round(bollinger.middle * 100000) / 100000 : null,\n  bb_lower: bollinger?.lower ? Math.round(bollinger.lower * 100000) / 100000 : null,\n  bb_bandwidth: bollinger?.bandwidth ? Math.round(bollinger.bandwidth * 100) / 100 : null,\n  \n  // Stochastic\n  stoch_k: stoch?.k ? Math.round(stoch.k * 100) / 100 : null,\n  stoch_d: stoch?.d ? Math.round(stoch.d * 100) / 100 : null,\n  \n  // Trend and Volatility\n  atr: atr ? Math.round(atr * 100000) / 100000 : null,\n  adx: adx?.adx ? Math.round(adx.adx * 100) / 100 : null,\n  di_plus: adx?.diPlus ? Math.round(adx.diPlus * 100) / 100 : null,\n  di_minus: adx?.diMinus ? Math.round(adx.diMinus * 100) / 100 : null,\n  \n  // Support and Resistance\n  nearest_support: nearestSupport ? Math.round(nearestSupport * 100000) / 100000 : null,\n  nearest_resistance: nearestResistance ? Math.round(nearestResistance * 100000) / 100000 : null,\n  \n  // Volume\n  current_volume: Math.round(currentVolume),\n  avg_volume: avgVolume ? Math.round(avgVolume) : null,\n  volume_ratio: Math.round(volumeRatio * 100) / 100,\n  \n  // Analysis Results\n  trend_direction: trendDirection,\n  overall_signal: overallSignal,\n  confidence_score: Math.round(confidenceScore * 100) / 100,\n  risk_level: riskLevel,\n  volatility_percentage: Math.round(volatility * 100) / 100,\n  \n  // Signals\n  active_signals: signals,\n  signal_count: signals.length,\n  buy_signals: buySignals.length,\n  sell_signals: sellSignals.length,\n  \n  // Data Quality\n  data_points_used: ohlcvData.length,\n  calculation_quality: ohlcvData.length >= 200 ? 'HIGH' : ohlcvData.length >= 100 ? 'MEDIUM' : 'LOW',\n  \n  status: 'SUCCESS'\n};\n\nreturn [{ json: result }];"}, "id": "advanced-technical-calculation", "name": "Advanced Technical Calculation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 240], "onError": "continueRegularOutput"}, {"parameters": {"model": "gpt-4o-mini", "options": {"temperature": 0.2, "maxTokens": 800}, "messages": {"messages": [{"role": "system", "content": "You are an elite technical analysis expert. Analyze the provided technical indicators and provide a concise professional assessment in JSON format with: market_phase, buy_sell_recommendation, confidence_level, key_insights, price_targets, and risk_assessment."}, {"role": "user", "content": "Technical Analysis Data: {{ JSON.stringify($json) }}"}]}}, "id": "ai-technical-interpretation", "name": "AI Technical Interpretation", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.4, "position": [1000, 240], "credentials": {"openAi": {"id": "openai-api", "name": "OpenAI API"}}, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Combine technical calculations with AI interpretation\nconst technicalData = $('Advanced Technical Calculation').first().json;\nconst aiInterpretation = $input.first().json;\n\n// Merge the data\nconst combinedAnalysis = {\n  ...technicalData,\n  ai_interpretation: aiInterpretation,\n  market_phase: aiInterpretation.market_phase || 'UNKNOWN',\n  ai_recommendation: aiInterpretation.buy_sell_recommendation || technicalData.overall_signal,\n  ai_confidence: aiInterpretation.confidence_level || technicalData.confidence_score,\n  key_insights: aiInterpretation.key_insights || [],\n  price_targets: aiInterpretation.price_targets || {},\n  ai_risk_assessment: aiInterpretation.risk_assessment || technicalData.risk_level,\n  \n  // Calculate final recommendation\n  final_recommendation: (() => {\n    const techSignal = technicalData.overall_signal;\n    const aiSignal = aiInterpretation.buy_sell_recommendation || techSignal;\n    \n    if (techSignal === aiSignal) {\n      return techSignal;\n    } else {\n      return 'HOLD'; // Conservative approach when signals conflict\n    }\n  })(),\n  \n  // Calculate final confidence\n  final_confidence: (() => {\n    const techConf = technicalData.confidence_score;\n    const aiConf = aiInterpretation.confidence_level || techConf;\n    \n    // Average confidence, but reduce if signals conflict\n    const avgConf = (techConf + aiConf) / 2;\n    const techSignal = technicalData.overall_signal;\n    const aiSignal = aiInterpretation.buy_sell_recommendation || techSignal;\n    \n    return techSignal === aiSignal ? avgConf : avgConf * 0.7;\n  })()\n};\n\nreturn [{ json: combinedAnalysis }];"}, "id": "combine-analysis", "name": "Combine Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 240], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO technical_analysis_enhanced (symbol, trend_direction, market_phase, buy_sell_signal, confidence_score, risk_level, rsi_value, macd_line, macd_signal, macd_histogram, bb_upper, bb_middle, bb_lower, support_level, resistance_level, volume_ratio, volatility_percentage, active_signals, ai_interpretation, final_recommendation, final_confidence, analysis_data, timestamp, data_quality_score) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, CURRENT_TIMESTAMP, $23) ON CONFLICT (symbol, DATE_TRUNC('minute', timestamp)) DO UPDATE SET trend_direction = EXCLUDED.trend_direction, confidence_score = EXCLUDED.confidence_score, analysis_data = EXCLUDED.analysis_data", "parameters": {"parameters": [{"name": "symbol", "value": "={{ $json.symbol }}"}, {"name": "trend_direction", "value": "={{ $json.trend_direction }}"}, {"name": "market_phase", "value": "={{ $json.market_phase }}"}, {"name": "buy_sell_signal", "value": "={{ $json.final_recommendation }}"}, {"name": "confidence_score", "value": "={{ $json.final_confidence }}"}, {"name": "risk_level", "value": "={{ $json.risk_level }}"}, {"name": "rsi_value", "value": "={{ $json.rsi }}"}, {"name": "macd_line", "value": "={{ $json.macd_line }}"}, {"name": "macd_signal", "value": "={{ $json.macd_signal }}"}, {"name": "macd_histogram", "value": "={{ $json.macd_histogram }}"}, {"name": "bb_upper", "value": "={{ $json.bb_upper }}"}, {"name": "bb_middle", "value": "={{ $json.bb_middle }}"}, {"name": "bb_lower", "value": "={{ $json.bb_lower }}"}, {"name": "support_level", "value": "={{ $json.nearest_support }}"}, {"name": "resistance_level", "value": "={{ $json.nearest_resistance }}"}, {"name": "volume_ratio", "value": "={{ $json.volume_ratio }}"}, {"name": "volatility_percentage", "value": "={{ $json.volatility_percentage }}"}, {"name": "active_signals", "value": "={{ JSON.stringify($json.active_signals) }}"}, {"name": "ai_interpretation", "value": "={{ JSON.stringify($json.ai_interpretation) }}"}, {"name": "final_recommendation", "value": "={{ $json.final_recommendation }}"}, {"name": "final_confidence", "value": "={{ $json.final_confidence }}"}, {"name": "analysis_data", "value": "={{ JSON.stringify($json) }}"}, {"name": "data_quality_score", "value": "={{ $json.calculation_quality === 'HIGH' ? 0.9 : $json.calculation_quality === 'MEDIUM' ? 0.7 : 0.5 }}"}]}, "options": {}}, "id": "store-analysis", "name": "Store Enhanced Analysis", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1400, 240], "credentials": {"postgres": {"id": "trading-postgres", "name": "Trading PostgreSQL"}}, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "high-confidence-signal", "leftValue": "={{ $json.final_confidence }}", "rightValue": 0.8, "operator": {"type": "number", "operation": "gte"}}, {"id": "strong-signal", "leftValue": "={{ $json.final_recommendation }}", "rightValue": "HOLD", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "id": "high-confidence-filter", "name": "High Confidence Filter", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1600, 240], "onError": "continueRegularOutput"}, {"parameters": {"url": "http://n8n-main:5678/webhook/master-strategist", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"trigger_type\": \"high_confidence_technical_signal\",\n  \"symbol\": $json.symbol,\n  \"signal\": $json.final_recommendation,\n  \"confidence\": $json.final_confidence,\n  \"trend\": $json.trend_direction,\n  \"risk_level\": $json.risk_level,\n  \"key_insights\": $json.key_insights,\n  \"price_targets\": $json.price_targets,\n  \"timestamp\": $json.timestamp\n}) }}", "options": {"timeout": 10000}}, "id": "trigger-master-strategist", "name": "Trigger Master Strategist", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1800, 160], "onError": "continueRegularOutput"}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify({\n  \"status\": \"success\",\n  \"symbol\": $json.symbol,\n  \"recommendation\": $json.final_recommendation,\n  \"confidence\": $json.final_confidence,\n  \"timestamp\": $json.timestamp\n}) }}"}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1800, 320], "onError": "continueRegularOutput"}], "connections": {"schedule-trigger": {"main": [[{"node": "get-analysis-symbols", "type": "main", "index": 0}]]}, "webhook-trigger": {"main": [[{"node": "get-analysis-symbols", "type": "main", "index": 0}]]}, "get-analysis-symbols": {"main": [[{"node": "split-symbols", "type": "main", "index": 0}]]}, "split-symbols": {"main": [[{"node": "get-ohlcv-data", "type": "main", "index": 0}]]}, "get-ohlcv-data": {"main": [[{"node": "advanced-technical-calculation", "type": "main", "index": 0}]]}, "advanced-technical-calculation": {"main": [[{"node": "ai-technical-interpretation", "type": "main", "index": 0}]]}, "ai-technical-interpretation": {"main": [[{"node": "combine-analysis", "type": "main", "index": 0}]]}, "combine-analysis": {"main": [[{"node": "store-analysis", "type": "main", "index": 0}]]}, "store-analysis": {"main": [[{"node": "high-confidence-filter", "type": "main", "index": 0}]]}, "high-confidence-filter": {"main": [[{"node": "trigger-master-strategist", "type": "main", "index": 0}, {"node": "success-response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": {"id": "error-handler-workflow"}}, "staticData": {}, "tags": [{"createdAt": "2025-01-22T10:00:00.000Z", "updatedAt": "2025-01-22T10:00:00.000Z", "id": "advanced-technical-analysis", "name": "Advanced Technical Analysis"}], "triggerCount": 2, "updatedAt": "2025-01-22T10:00:00.000Z", "versionId": "2.0.0"}