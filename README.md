# 🚀 Multi-Agent AI Trading System

## 📋 Proje Açıklaması

Bu proje, n8n workflow automation platformu kullanarak geliştirilmiş, çok ajanli (multi-agent) bir kripto para trading sistemidir. <PERSON><PERSON>m, farklı uzmanlık alanlarında çalışan AI ajanlarından oluşur ve birbirleriyle koordineli şekilde çalışarak akıllı trading kararları verir.

## 🏗️ Sistem Mimarisi

### 🤖 AI Ajanları

1. **🕵️ Data Harvester Agent** - Veri Toplayıcı
   - Her 2 dakikada bir çalışır
   - Binance, CoinGecko, Fear & Greed Index, Crypto News toplar
   - AI ile veri kalitesi analizi yapar
   - Hata yönetimi ve API dayanıklılığı

2. **📈 Advanced Technical Analyst Agent** - Teknik Analist
   - Her 5 dakikada bir çalışır
   - Hibrit yaklaşım: Kod ile kesin hesaplama + AI yorumlama
   - RSI, MACD, <PERSON><PERSON><PERSON>, SMA, EMA hesaplar
   - <PERSON><PERSON><PERSON><PERSON><PERSON> sinyallerde Master Strategist'i tetikler

3. **📰 Sentiment Analyst Agent** - Duygu Analisti
   - Her 10 dakikada bir çalışır
   - Haber ve sosyal medya duygu analizi
   - Döngüsel haber işleme (20+ haber varsa kendini tetikler)
   - Aşırı duygu durumlarında uyarı verir

4. **🛡️ Risk Manager Agent** - Risk Yöneticisi
   - Her 3 dakikada bir çalışır
   - Portföy risk analizi ve volatilite kontrolü
   - Yüksek risk durumunda acil uyarı
   - Telegram ile kritik uyarılar

5. **🧠 Master Strategist Brain** - Ana Beyin
   - Her 15 dakikada bir + webhook tetikleyicisi
   - Tüm agent verilerini sentezler
   - Çelişkileri çözer, gizli pattern'leri bulur
   - Yüksek güvenli stratejileri onaylar

6. **💬 Telegram Herald Assistant** - Çılgın Asistan
   - Webhook + Telegram komutları
   - Çılgın asistan kişiliği
   - Proaktif ve reaktif mesajlaşma
   - Inline keyboard ile etkileşimli arayüz

### 🔄 Veri Akışı

```
Data Harvester → PostgreSQL ← Technical Analyst
                     ↓              ↓
                Master Strategist ← Sentiment Analyst
                     ↓              ↓
              Telegram Herald ← Risk Manager
```

## 🛠️ Teknoloji Stack

- **Workflow Engine**: n8n
- **Database**: PostgreSQL
- **AI Models**: OpenAI GPT-4, Anthropic Claude
- **APIs**: Binance, CoinGecko, CryptoPanic, Alternative.me
- **Messaging**: Telegram Bot API
- **Languages**: JavaScript (Node.js), SQL

## 📦 Kurulum

### Gereksinimler

- n8n (Docker veya self-hosted)
- PostgreSQL 12+
- Node.js 18+
- API Keys:
  - OpenAI API Key
  - Telegram Bot Token
  - Binance API (opsiyonel)
  - CoinGecko API (opsiyonel)

### 1. Veritabanı Kurulumu

```sql
-- Veritabanı şeması database/schema.sql dosyasında
```

### 2. n8n Kurulumu

```bash
# Docker ile n8n kurulumu
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -e DB_TYPE=postgresdb \
  -e DB_POSTGRESDB_HOST=localhost \
  -e DB_POSTGRESDB_PORT=5432 \
  -e DB_POSTGRESDB_DATABASE=n8n_trading \
  -e DB_POSTGRESDB_USER=n8n_user \
  -e DB_POSTGRESDB_PASSWORD=your_password \
  n8nio/n8n
```

### 3. Workflow'ları İçe Aktarma

1. n8n arayüzüne gidin (http://localhost:5678)
2. `workflows/` klasöründeki JSON dosyalarını içe aktarın
3. Credentials'ları yapılandırın
4. Workflow'ları sırayla aktif hale getirin

## 🔧 Yapılandırma

### API Credentials

n8n'de aşağıdaki credentials'ları oluşturun:

1. **OpenAI API**
   - Type: OpenAI
   - API Key: your_openai_api_key

2. **Telegram Bot**
   - Type: Telegram Bot
   - Access Token: your_telegram_bot_token
   - Chat ID: your_chat_id

3. **PostgreSQL**
   - Type: Postgres
   - Host: localhost
   - Port: 5432
   - Database: n8n_trading
   - User: n8n_user
   - Password: your_password

## 🚀 Kullanım

### Sistem Başlatma

1. PostgreSQL'i başlatın
2. n8n'i başlatın
3. Workflow'ları sırayla aktif hale getirin:
   - Data Harvester Agent
   - Technical Analyst Agent
   - Sentiment Analyst Agent
   - Risk Manager Agent
   - Master Strategist Brain
   - Telegram Herald Assistant

### Telegram Komutları

- `/status` - Genel sistem durumu
- `/analiz` - Detaylı teknik analiz
- `/risk` - Risk durumu raporu
- `/strateji` - Son stratejiler
- `/help` - Yardım menüsü

## 📊 Özellikler

### ✅ Gelişmiş Özellikler

- **Hibrit Zeka**: Kodun kesinliği + AI'ın yorumlama gücü
- **Veri Kalitesi Kontrolü**: Düşük kaliteli veri otomatik filtreleme
- **API Dayanıklılığı**: Hata yönetimi ve retry mekanizması
- **Döngüsel İşleme**: Büyük veri akışları için otomatik döngü
- **Etkileşimli Arayüz**: Telegram inline keyboard
- **Gerçek Zamanlı Uyarılar**: Kritik durumlar için anında bildirim

### 🛡️ Güvenlik

- Continue on Fail: API hatalarında sistem durmuyor
- Retry Mechanism: Başarısız istekler otomatik tekrarlanıyor
- Data Validation: Veri kalitesi sürekli kontrol ediliyor
- Error Logging: Tüm hatalar loglanıyor ve raporlanıyor

## 📈 Performans

- **Veri Toplama**: 2 dakikada bir
- **Teknik Analiz**: 5 dakikada bir
- **Duygu Analizi**: 10 dakikada bir
- **Risk Kontrolü**: 3 dakikada bir
- **Strateji Sentezi**: 15 dakikada bir

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## 👨‍💻 Geliştirici

**inkbytefo** - [GitHub](https://github.com/inkbytefo)

## 🙏 Teşekkürler

- n8n Community
- OpenAI
- Binance API
- CoinGecko API
- Telegram Bot API

---

⚠️ **Uyarı**: Bu sistem eğitim ve araştırma amaçlıdır. Gerçek trading kararları vermeden önce sistemi test edin ve risk yönetimi uygulayın.
