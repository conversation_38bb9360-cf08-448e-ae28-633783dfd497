#!/usr/bin/env node

/**
 * Health Check Endpoint
 * Author: inkbytefo
 * Description: Simple HTTP health check server for Docker health checks
 */

const http = require('http');
const { execSync } = require('child_process');

const PORT = process.env.HEALTH_PORT || 3000;

function checkDatabase() {
  try {
    const dbConfig = {
      host: process.env.DB_POSTGRESDB_HOST || 'localhost',
      port: process.env.DB_POSTGRESDB_PORT || 5432,
      database: process.env.DB_POSTGRESDB_DATABASE || 'n8n_trading',
      user: process.env.DB_POSTGRESDB_USER || 'n8n_user',
      password: process.env.DB_POSTGRESDB_PASSWORD
    };

    if (!dbConfig.password) {
      return { status: 'warning', message: 'Database password not configured' };
    }

    const testCommand = `PGPASSWORD=${dbConfig.password} psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.user} -d ${dbConfig.database} -c "SELECT 1;" -t`;
    execSync(testCommand, { stdio: 'pipe', timeout: 5000 });
    
    return { status: 'healthy', message: 'Database connection OK' };
  } catch (error) {
    return { status: 'unhealthy', message: `Database connection failed: ${error.message}` };
  }
}

function checkN8n() {
  try {
    const n8nHost = process.env.N8N_HOST || 'localhost';
    const n8nPort = process.env.N8N_PORT || 5678;
    const protocol = process.env.N8N_PROTOCOL || 'http';
    
    const curlCommand = `curl -s -o /dev/null -w "%{http_code}" --max-time 5 ${protocol}://${n8nHost}:${n8nPort}/healthz`;
    const statusCode = execSync(curlCommand, { encoding: 'utf8', stdio: 'pipe', timeout: 5000 }).trim();
    
    if (statusCode === '200') {
      return { status: 'healthy', message: 'n8n is responding' };
    } else {
      return { status: 'unhealthy', message: `n8n returned status code: ${statusCode}` };
    }
  } catch (error) {
    return { status: 'unhealthy', message: `n8n connection failed: ${error.message}` };
  }
}

function checkEnvironment() {
  const requiredEnvVars = [
    'DB_POSTGRESDB_HOST',
    'DB_POSTGRESDB_DATABASE',
    'DB_POSTGRESDB_USER',
    'OPENAI_API_KEY',
    'TELEGRAM_BOT_TOKEN'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    return { 
      status: 'warning', 
      message: `Missing environment variables: ${missingVars.join(', ')}` 
    };
  }
  
  return { status: 'healthy', message: 'Environment variables OK' };
}

function getSystemInfo() {
  try {
    const uptime = process.uptime();
    const memUsage = process.memoryUsage();
    
    return {
      uptime: Math.floor(uptime),
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      },
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };
  } catch (error) {
    return { error: error.message };
  }
}

function performHealthCheck() {
  const checks = {
    environment: checkEnvironment(),
    database: checkDatabase(),
    n8n: checkN8n()
  };
  
  const overallStatus = Object.values(checks).every(check => check.status === 'healthy') 
    ? 'healthy' 
    : Object.values(checks).some(check => check.status === 'unhealthy')
    ? 'unhealthy'
    : 'warning';
  
  return {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    version: require('../package.json').version,
    checks,
    system: getSystemInfo()
  };
}

// HTTP Server
const server = http.createServer((req, res) => {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  if (req.url === '/health' && req.method === 'GET') {
    try {
      const healthCheck = performHealthCheck();
      const statusCode = healthCheck.status === 'healthy' ? 200 : 
                        healthCheck.status === 'warning' ? 200 : 503;
      
      res.writeHead(statusCode, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(healthCheck, null, 2));
    } catch (error) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString()
      }, null, 2));
    }
  } else if (req.url === '/ping' && req.method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('pong');
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      error: 'Not Found',
      message: 'Available endpoints: /health, /ping'
    }, null, 2));
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Health check server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Health check server closed');
    process.exit(0);
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`Health check server running on port ${PORT}`);
  console.log(`Health endpoint: http://localhost:${PORT}/health`);
  console.log(`Ping endpoint: http://localhost:${PORT}/ping`);
});

module.exports = { performHealthCheck };
