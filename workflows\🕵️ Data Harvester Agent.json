{"name": "🕵️ Data Harvester Agent", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 2}]}}, "id": "schedule-trigger", "name": "Every 2 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-64, 256], "onError": "continueRegularOutput"}, {"parameters": {"url": "https://api.binance.com/api/v3/ticker/24hr?symbols=[\"BTCUSDT\",\"ETHUSDT\",\"ADAUSDT\",\"XRPUSDT\",\"SOLUSDT\"]", "options": {"timeout": 10000}}, "id": "binance-data", "name": "Get Binance Market Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [272, 48], "onError": "continueRegularOutput"}, {"parameters": {"url": "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin,ethereum,cardano,ripple,solana&vs_currencies=usd&include_market_cap=true&include_24hr_vol=true&include_24hr_change=true", "options": {"timeout": 10000}}, "id": "coingecko-data", "name": "Get CoinGecko Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [272, 192], "onError": "continueRegularOutput"}, {"parameters": {"url": "https://api.alternative.me/fng/", "options": {"timeout": 10000}}, "id": "fear-greed-index", "name": "Get Fear & Greed Index", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [272, 336], "onError": "continueRegularOutput"}, {"parameters": {"url": "https://cryptopanic.com/api/v1/posts/?auth_token=demo&public=true&kind=news&currencies=BTC,ETH,ADA,XRP,SOL", "options": {"timeout": 15000}}, "id": "crypto-news", "name": "Get Crypto News", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [256, 512], "onError": "continueRegularOutput"}, {"parameters": {}, "id": "merge-data", "name": "Merge All Data", "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [576, 368], "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "Sen bir kripto para veri kalitesi uzmanısın. Aşağıdaki tüm veri kaynaklarından gelen verileri analiz et:\n\n{{JSON.stringify($json, null, 2)}}\n\nGörevlerin:\n1. <PERSON><PERSON> ka<PERSON><PERSON><PERSON> (0-1 a<PERSON><PERSON> skor)\n2. Anomalileri tespit et\n3. Veri tutarlılığını kontrol et\n4. Eksik verileri belirle\n5. Piyasa sinyallerini çıkar\n6. Öneriler sun\n\nJSON formatında kapsamlı kalite raporu sun.", "hasOutputParser": true, "options": {}}, "id": "ai-data-processor", "name": "AI Data Quality Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [576, 96], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// AI Data Quality Agent çıktısını database için hazırla\nconst aiOutput = $input.first().json;\n\n// Veri kalitesi skorunu çıkar\nlet qualityScore = 0.5; // default\nif (aiOutput.data_quality_score) {\n  qualityScore = aiOutput.data_quality_score;\n} else if (aiOutput.quality_score) {\n  qualityScore = aiOutput.quality_score;\n}\n\n// Güvenli string escape fonksiyonu\nfunction escapeString(str) {\n  if (typeof str !== 'string') return str;\n  return str.replace(/'/g, \"''\");\n}\n\n// Database query'sini hazırla\nconst dbData = {\n  symbol: 'MULTI_SOURCE',\n  price: 1,\n  volume_24h: 1,\n  price_change_percentage_24h: 1,\n  source: 'data_harvester',\n  ai_quality_score: qualityScore,\n  ai_analysis_escaped: escapeString(JSON.stringify(aiOutput))\n};\n\nreturn { json: dbData };"}, "id": "prepare-db-data", "name": "Prepare Database Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 96], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO market_data (symbol, price, volume_24h, price_change_percentage_24h, source, timestamp, ai_quality_score, ai_analysis) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, $6, $7)", "options": {}}, "id": "save-to-db", "name": "Save to Database", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1024, 96], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "quality-threshold", "leftValue": "={{ $json.data_quality_score || $json.quality_score || 0.5 }}", "rightValue": 0.7, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "data-quality-filter", "name": "Data Quality Filter", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [864, 336], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "const qualityData = $input.all()[0].json;\nconst markedData = {\n  ...qualityData,\n  data_reliability: 'LOW',\n  weight_factor: 0.3,\n  quality_issues: qualityData.anomalies_detected || [],\n  recommendation: 'USE_WITH_CAUTION'\n};\nreturn [{ json: markedData }];"}, "id": "mark-low-quality", "name": "Mark Low Quality Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1104, 496], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "const qualityData = $input.all()[0].json;\nconst markedData = {\n  ...qualityData,\n  data_reliability: 'HIGH',\n  weight_factor: 1.0,\n  quality_certification: 'VERIFIED',\n  recommendation: 'SAFE_TO_USE'\n};\nreturn [{ json: markedData }];"}, "id": "mark-high-quality", "name": "Mark High Quality Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1104, 208], "onError": "continueRegularOutput"}, {"parameters": {}, "id": "merge-quality-data", "name": "Merge Quality Data", "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1392, 336], "onError": "continueRegularOutput"}], "pinData": {}, "connections": {"Every 2 Minutes": {"main": [[{"node": "Get Binance Market Data", "type": "main", "index": 0}, {"node": "Get CoinGecko Data", "type": "main", "index": 0}, {"node": "Get Fear & Greed Index", "type": "main", "index": 0}, {"node": "Get Crypto News", "type": "main", "index": 0}]]}, "Get Binance Market Data": {"main": [[{"node": "Merge All Data", "type": "main", "index": 0}]]}, "Get CoinGecko Data": {"main": [[{"node": "Merge All Data", "type": "main", "index": 1}]]}, "Get Fear & Greed Index": {"main": [[{"node": "Merge All Data", "type": "main", "index": 2}]]}, "Get Crypto News": {"main": [[{"node": "Merge All Data", "type": "main", "index": 3}]]}, "Merge All Data": {"main": [[{"node": "AI Data Quality Agent", "type": "main", "index": 0}]]}, "AI Data Quality Agent": {"main": [[{"node": "Prepare Database Data", "type": "main", "index": 0}, {"node": "Data Quality Filter", "type": "main", "index": 0}]]}, "Prepare Database Data": {"main": [[{"node": "Save to Database", "type": "main", "index": 0}]]}, "Data Quality Filter": {"main": [[{"node": "Mark High Quality Data", "type": "main", "index": 0}], [{"node": "Mark Low Quality Data", "type": "main", "index": 0}]]}, "Mark High Quality Data": {"main": [[{"node": "Merge Quality Data", "type": "main", "index": 0}]]}, "Mark Low Quality Data": {"main": [[{"node": "Merge Quality Data", "type": "main", "index": 1}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": true, "saveExecutionProgress": true}, "versionId": "c3b976a8-4d5d-4fb3-8c7c-daf2f79a2886", "meta": {"instanceId": "a714df1aa11da563210a0570d83019f8d8c6597fc9f1fe509b3525f7215ea45e"}, "id": "VskGYqdKoHKvZP50", "tags": []}