version: '3.8'

services:
  # PostgreSQL Database with TimescaleDB
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: trading-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n_trading
      POSTGRES_USER: n8n_user
      POSTGRES_PASSWORD: secure_password_123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
      # TimescaleDB Configuration
      TIMESCALEDB_TELEMETRY: "off"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/timescale-init.sql:/docker-entrypoint-initdb.d/02-timescale.sql:ro
      - ./database/optimizations.sql:/docker-entrypoint-initdb.d/03-optimizations.sql:ro
    ports:
      - "5432:5432"
    networks:
      - trading-network
    command: >
      postgres
      -c shared_preload_libraries=timescaledb
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n_user -d n8n_trading"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # n8n Main Instance (Queue Mode)
  n8n-main:
    image: n8nio/n8n:latest
    container_name: trading-n8n-main
    restart: unless-stopped
    environment:
      # Database Configuration
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n_trading
      DB_POSTGRESDB_USER: n8n_user
      DB_POSTGRESDB_PASSWORD: secure_password_123

      # n8n Configuration
      N8N_HOST: localhost
      N8N_PORT: 5678
      N8N_PROTOCOL: http
      WEBHOOK_URL: http://localhost:5678/

      # Security
      N8N_ENCRYPTION_KEY: your_encryption_key_here_32_chars
      N8N_USER_MANAGEMENT_DISABLED: "true"

      # Queue Mode Configuration
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      QUEUE_BULL_REDIS_PASSWORD: redis_password
      EXECUTIONS_MODE: queue

      # Performance Optimizations
      EXECUTIONS_PROCESS: main
      EXECUTIONS_DATA_SAVE_ON_ERROR: all
      EXECUTIONS_DATA_SAVE_ON_SUCCESS: all
      EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS: true
      EXECUTIONS_DATA_MAX_AGE: 168  # 7 days
      EXECUTIONS_DATA_PRUNE: true

      # Worker Configuration
      N8N_WORKERS_COUNT: 2
      N8N_WORKER_TIMEOUT: 120

      # Logging
      N8N_LOG_LEVEL: info
      N8N_LOG_OUTPUT: console
      N8N_LOG_FILE_COUNT_MAX: 100
      N8N_LOG_FILE_SIZE_MAX: 16

      # Timezone
      GENERIC_TIMEZONE: Europe/Istanbul
      TZ: Europe/Istanbul
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/workflows:ro
    networks:
      - trading-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # n8n Worker Instances
  n8n-worker-1:
    image: n8nio/n8n:latest
    container_name: trading-n8n-worker-1
    restart: unless-stopped
    command: n8n worker
    environment:
      # Database Configuration
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n_trading
      DB_POSTGRESDB_USER: n8n_user
      DB_POSTGRESDB_PASSWORD: secure_password_123

      # Queue Configuration
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      QUEUE_BULL_REDIS_PASSWORD: redis_password

      # Worker Settings
      N8N_ENCRYPTION_KEY: your_encryption_key_here_32_chars
      EXECUTIONS_MODE: queue

      # Performance
      N8N_WORKER_TIMEOUT: 120
      N8N_LOG_LEVEL: info

      # Timezone
      GENERIC_TIMEZONE: Europe/Istanbul
      TZ: Europe/Istanbul
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - trading-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep 'n8n worker' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  n8n-worker-2:
    image: n8nio/n8n:latest
    container_name: trading-n8n-worker-2
    restart: unless-stopped
    command: n8n worker
    environment:
      # Database Configuration
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n_trading
      DB_POSTGRESDB_USER: n8n_user
      DB_POSTGRESDB_PASSWORD: secure_password_123

      # Queue Configuration
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      QUEUE_BULL_REDIS_PASSWORD: redis_password

      # Worker Settings
      N8N_ENCRYPTION_KEY: your_encryption_key_here_32_chars
      EXECUTIONS_MODE: queue

      # Performance
      N8N_WORKER_TIMEOUT: 120
      N8N_LOG_LEVEL: info

      # Timezone
      GENERIC_TIMEZONE: Europe/Istanbul
      TZ: Europe/Istanbul
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - trading-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep 'n8n worker' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Trading System Application
  trading-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trading-app
    restart: unless-stopped
    environment:
      # Database Configuration
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n_trading
      DB_POSTGRESDB_USER: n8n_user
      DB_POSTGRESDB_PASSWORD: secure_password_123

      # n8n Configuration
      N8N_HOST: n8n
      N8N_PORT: 5678
      N8N_PROTOCOL: http

      # System Configuration
      NODE_ENV: production
      LOG_LEVEL: info
      TZ: Europe/Istanbul

      # Trading Configuration
      DEFAULT_RISK_LEVEL: medium
      MAX_POSITION_SIZE: 0.1
      STOP_LOSS_PERCENTAGE: 5
      TAKE_PROFIT_PERCENTAGE: 15

      # Security
      WEBHOOK_SECRET: your_webhook_secret_here
      API_RATE_LIMIT: 100
    ports:
      - "3000:3000"
    volumes:
      - app_logs:/app/logs
      - app_backups:/app/backups
    networks:
      - trading-network
    depends_on:
      postgres:
        condition: service_healthy
      n8n:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis for Caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: trading-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  n8n_data:
    driver: local
  app_logs:
    driver: local
  app_backups:
    driver: local
  redis_data:
    driver: local

networks:
  trading-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
