{"name": "🛡️ Risk Manager Agent", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 3}]}}, "id": "schedule-trigger", "name": "Every 3 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-16, 384], "onError": "continueRegularOutput"}, {"parameters": {"url": "https://api.binance.com/api/v3/account", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "id": "get-portfolio-data", "name": "Get Portfolio Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [208, 128], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT symbol, price, volume_24h, price_change_percentage_24h FROM market_data WHERE timestamp > NOW() - INTERVAL '24 hours' ORDER BY timestamp DESC", "options": {}}, "id": "get-market-volatility", "name": "Get Market Volatility", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [192, 448], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {}, "id": "merge-risk-data", "name": "Merge Risk Data", "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [416, 304], "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "Sen bir kripto para risk yönetimi uzmanısın. Aşağıdaki portföy ve piyasa verilerini analiz et:\n\n{{JSON.stringify($json, null, 2)}}\n\nGörevlerin:\n1. Portföy risk seviyesini değerlendir\n2. Volatilite analizi yap\n3. Pozisyon boyutlarını kontrol et\n4. Stop-loss önerilerini hesapla\n5. Genel risk skorunu belirle\n6. Acil eylem gereksinimlerini tespit et\n\nJSON formatında risk yönetimi raporu sun.", "hasOutputParser": true, "options": {}}, "id": "ai-risk-manager", "name": "AI Risk Management Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [432, 160], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// AI Risk Management Agent çıktısını database için hazırla\nconst riskOutput = $input.first().json;\n\n// Güvenli string escape fonksiyonu\nfunction escapeString(str) {\n  if (typeof str !== 'string') return str;\n  return str.replace(/'/g, \"''\");\n}\n\n// Database için veri hazırla\nconst dbData = {\n  portfolio_risk_score: riskOutput.portfolio_risk_score || 0.5,\n  volatility_level: riskOutput.volatility_level || 'MEDIUM',\n  overall_risk_level: riskOutput.overall_risk_level || 'MEDIUM',\n  max_daily_loss_limit: riskOutput.max_daily_loss_limit || 0,\n  confidence_level: riskOutput.confidence_level || 0.5,\n  assessment_data_escaped: escapeString(JSON.stringify(riskOutput))\n};\n\nreturn { json: dbData };"}, "id": "prepare-risk-data", "name": "Prepare Risk Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [656, 160], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO risk_assessments (portfolio_risk_score, volatility_level, overall_risk_level, max_daily_loss_limit, confidence_level, assessment_data, timestamp) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)", "options": {}}, "id": "save-risk-assessment", "name": "Save Risk Assessment", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [864, 176], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "high-risk", "leftValue": "={{ $('Prepare Risk Data').first().json.portfolio_risk_score }}", "rightValue": 0.7, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "check-high-risk", "name": "Check High Risk", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [832, 384], "onError": "continueRegularOutput"}, {"parameters": {"chatId": "={{ $credentials.telegram_bot.chat_id }}", "text": "🚨 **ACİL RİSK UYARISI!** 🚨\n\n⚠️ **Risk Seviyesi:** {{ $('AI Risk Management Agent').first().json.overall_risk_level }}\n📊 **Risk Skoru:** {{ Math.round($('AI Risk Management Agent').first().json.portfolio_risk_score * 100) }}%\n💥 **Volatilite:** {{ $('AI Risk Management Agent').first().json.volatility_level }}\n\n🔥 **ACİL EYLEMLER:**\n{{ ($('AI Risk Management Agent').first().json.urgent_actions_needed || []).map(a => '• ' + a).join('\\n') }}\n\n💡 **ÖNERİLER:**\n{{ ($('AI Risk Management Agent').first().json.risk_recommendations || []).map(r => '• ' + r).join('\\n') }}\n\n🤖 *Risk Manager Agent*", "additionalFields": {}}, "id": "emergency-alert", "name": "Send Emergency Alert", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1104, 352], "webhookId": "b95ef1e6-a13b-44ba-8133-4f03d5a96dfb", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/master-strategist", "sendBody": true, "bodyParameters": {"parameters": [{"name": "alert_type", "value": "high_risk_detected"}, {"name": "source_agent", "value": "risk_manager"}, {"name": "risk_data", "value": "={{ JSON.stringify($('AI Risk Management Agent').first().json) }}"}, {"name": "urgency", "value": "CRITICAL"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "trigger-strategist", "name": "Trigger Master Strategist", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1024, 544], "onError": "continueRegularOutput"}], "pinData": {}, "connections": {"Every 3 Minutes": {"main": [[{"node": "Get Portfolio Data", "type": "main", "index": 0}, {"node": "Get Market Volatility", "type": "main", "index": 0}]]}, "Get Portfolio Data": {"main": [[{"node": "Merge Risk Data", "type": "main", "index": 0}]]}, "Get Market Volatility": {"main": [[{"node": "Merge Risk Data", "type": "main", "index": 1}]]}, "Merge Risk Data": {"main": [[{"node": "AI Risk Management Agent", "type": "main", "index": 0}]]}, "AI Risk Management Agent": {"main": [[{"node": "Prepare Risk Data", "type": "main", "index": 0}]]}, "Prepare Risk Data": {"main": [[{"node": "Save Risk Assessment", "type": "main", "index": 0}, {"node": "Check High Risk", "type": "main", "index": 0}]]}, "Check High Risk": {"main": [[{"node": "Send Emergency Alert", "type": "main", "index": 0}, {"node": "Trigger Master Strategist", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": true, "saveExecutionProgress": true}, "versionId": "06a50955-f151-4afb-873c-bb081c6c8277", "meta": {"instanceId": "a714df1aa11da563210a0570d83019f8d8c6597fc9f1fe509b3525f7215ea45e"}, "id": "jPp5caUNMU6Uvtt2", "tags": []}