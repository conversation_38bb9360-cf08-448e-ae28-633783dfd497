# Docker Deployment Guide

Multi-Agent AI Trading System için kapsamlı Docker deployment rehberi.

## 📋 İçindekiler

- [<PERSON><PERSON><PERSON><PERSON><PERSON>](#gereksinimler)
- [Hızl<PERSON> Başlangıç](#hızlı-başlangıç)
- [Development Ortamı](#development-ortamı)
- [Production Deployment](#production-deployment)
- [Güvenlik Yapılandırması](#güvenlik-yapılandırması)
- [Monitoring ve Logging](#monitoring-ve-logging)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## 🔧 Gereksinimler

### Sistem Gereksinimleri

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **RAM**: Minimum 4GB, Önerilen 8GB+
- **Disk**: Minimum 20GB boş alan
- **CPU**: 2+ cores önerilir

### Kurulum

```bash
# Docker kurulumu (Ubuntu/Debian)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Docker Compose kurulumu
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 🚀 Hızlı Başlangıç

### 1. Repository'yi Klonlayın

```bash
git clone https://github.com/inkbytefo/multi-agent-ai-trading-system.git
cd multi-agent-ai-trading-system
```

### 2. Environment Dosyasını Oluşturun

```bash
cp .env.example .env
# .env dosyasını API anahtarlarınızla güncelleyin
```

### 3. Development Ortamını Başlatın

```bash
docker compose up -d
```

### 4. Servislerin Durumunu Kontrol Edin

```bash
docker compose ps
docker compose logs -f
```

## 🛠️ Development Ortamı

### Servisler

- **PostgreSQL**: Port 5432
- **n8n**: Port 5678
- **Trading App**: Port 3000
- **Redis**: Port 6379

### Komutlar

```bash
# Servisleri başlat
docker compose up -d

# Logları izle
docker compose logs -f [service_name]

# Servisleri durdur
docker compose down

# Volumes ile birlikte temizle
docker compose down -v

# Yeniden build et
docker compose build --no-cache

# Belirli bir servisi yeniden başlat
docker compose restart [service_name]
```

### Volume Yönetimi

```bash
# Volume'ları listele
docker volume ls

# Volume içeriğini incele
docker run --rm -v trading_postgres_data:/data alpine ls -la /data

# Backup oluştur
docker run --rm -v trading_postgres_data:/data -v $(pwd)/backups:/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .
```

## 🏭 Production Deployment

### 1. Secrets Hazırlığı

```bash
# Secrets klasörü oluştur
mkdir -p secrets

# API anahtarlarını dosyalara yaz
echo "your_openai_api_key" > secrets/openai_api_key.txt
echo "your_telegram_bot_token" > secrets/telegram_bot_token.txt

# Güvenli izinler ayarla
chmod 600 secrets/*.txt
```

### 2. SSL Sertifikaları

```bash
# SSL klasörü oluştur
mkdir -p nginx/ssl

# Sertifikalarınızı yerleştirin
cp your_cert.pem nginx/ssl/cert.pem
cp your_key.pem nginx/ssl/key.pem

# İzinleri ayarla
chmod 600 nginx/ssl/*.pem
```

### 3. Production Environment

```bash
# Production .env dosyası oluştur
cp .env.example .env.production

# Production değerlerini ayarla
NODE_ENV=production
LOG_LEVEL=warn
N8N_PROTOCOL=https
N8N_HOST=your-domain.com
```

### 4. Deployment

```bash
# Otomatik deployment
node scripts/deploy.js

# Manuel deployment
docker compose -f docker-compose.prod.yml up -d

# Deployment durumunu kontrol et
docker compose -f docker-compose.prod.yml ps
```

## 🔒 Güvenlik Yapılandırması

### Container Güvenliği

- **Non-root user**: Tüm containerlar non-root user ile çalışır
- **Read-only filesystem**: Mümkün olduğunda read-only mount
- **Resource limits**: CPU ve memory limitleri
- **Health checks**: Otomatik sağlık kontrolleri

### Network Güvenliği

```bash
# Firewall kuralları (UFW)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 5432/tcp   # PostgreSQL (sadece internal)
sudo ufw deny 5678/tcp   # n8n (sadece internal)
sudo ufw enable
```

### Secrets Yönetimi

```bash
# Docker secrets kullanımı
echo "your_secret" | docker secret create my_secret -

# Compose file'da kullanım
services:
  app:
    secrets:
      - my_secret
```

## 📊 Monitoring ve Logging

### Health Monitoring

```bash
# Sistem durumunu kontrol et
node scripts/monitor.js

# Sürekli monitoring
node scripts/monitor.js --continuous

# Container health check
docker compose -f docker-compose.prod.yml ps
```

### Log Yönetimi

```bash
# Tüm servislerin logları
docker compose logs -f

# Belirli servis logları
docker compose logs -f trading-app

# Log rotation ayarları
# docker-compose.prod.yml içinde:
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### Metrics Collection

```bash
# Container stats
docker stats

# Resource usage
docker system df

# Disk usage
du -sh /var/lib/docker/
```

## 🔧 Troubleshooting

### Yaygın Sorunlar

#### 1. Container Başlamıyor

```bash
# Logları kontrol et
docker compose logs [service_name]

# Container'ı debug mode'da çalıştır
docker run -it --rm [image_name] /bin/sh
```

#### 2. Database Bağlantı Sorunu

```bash
# PostgreSQL bağlantısını test et
docker compose exec postgres psql -U n8n_user -d n8n_trading -c "SELECT 1;"

# Network bağlantısını kontrol et
docker compose exec trading-app ping postgres
```

#### 3. Port Çakışması

```bash
# Kullanılan portları kontrol et
netstat -tulpn | grep :5678

# Alternatif port kullan
N8N_PORT=5679 docker compose up -d
```

#### 4. Disk Alanı Sorunu

```bash
# Docker temizliği
docker system prune -a

# Kullanılmayan volume'ları temizle
docker volume prune

# Log dosyalarını temizle
docker compose exec trading-app find /app/logs -name "*.log" -mtime +7 -delete
```

### Debug Komutları

```bash
# Container içine gir
docker compose exec [service_name] /bin/sh

# Environment variables'ları kontrol et
docker compose exec [service_name] env

# Network bağlantılarını test et
docker compose exec [service_name] nslookup postgres

# File permissions kontrol et
docker compose exec [service_name] ls -la /app
```

## 📚 Best Practices

### 1. Image Optimization

```dockerfile
# Multi-stage build kullan
FROM node:18-alpine AS builder
# ... build steps

FROM node:18-alpine AS production
# ... production setup
```

### 2. Security

- Secrets'ları environment variables yerine files olarak kullan
- Regular security updates
- Container image scanning
- Least privilege principle

### 3. Performance

```yaml
# Resource limits ayarla
deploy:
  resources:
    limits:
      memory: 1G
      cpus: '0.5'
    reservations:
      memory: 512M
      cpus: '0.25'
```

### 4. Backup Strategy

```bash
# Otomatik backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker compose exec -T postgres pg_dump -U n8n_user n8n_trading > backup_${DATE}.sql
```

### 5. Update Strategy

```bash
# Rolling update
docker compose -f docker-compose.prod.yml pull
docker compose -f docker-compose.prod.yml up -d --no-deps trading-app
```

## 🆘 Destek

### Loglar

- **Application logs**: `logs/` klasörü
- **Container logs**: `docker compose logs`
- **System logs**: `/var/log/`

### Monitoring

- **Health checks**: `node scripts/monitor.js`
- **Resource usage**: `docker stats`
- **Disk usage**: `df -h`

### İletişim

- **GitHub Issues**: [Repository Issues](https://github.com/inkbytefo/multi-agent-ai-trading-system/issues)
- **Email**: <EMAIL>

---

**Not**: Bu rehber production ortamında güvenli ve stabil bir deployment için hazırlanmıştır. Herhangi bir sorun yaşarsanız lütfen GitHub Issues'da bildirin.
