#!/usr/bin/env node

/**
 * Security Setup Script for n8n Trading System
 * Author: inkbytefo
 * Description: Implements security best practices and monitoring
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

// Color functions for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function generateSecureKey(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

function generateJWTSecret() {
  return crypto.randomBytes(64).toString('base64');
}

function createSecurityConfig() {
  log('🔐 Güvenlik yapılandırması oluşturuluyor...', 'blue');
  
  const securityConfig = {
    encryption: {
      n8n_encryption_key: generateSecureKey(32),
      webhook_secret: generateSecureKey(16),
      jwt_secret: generateJWTSecret()
    },
    api_security: {
      rate_limit: {
        window_ms: 900000, // 15 minutes
        max_requests: 100,
        skip_successful_requests: false
      },
      cors: {
        origin: ['http://localhost:5678', 'https://your-domain.com'],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
        allowed_headers: ['Content-Type', 'Authorization', 'X-Webhook-Signature']
      }
    },
    webhook_security: {
      signature_validation: true,
      ip_whitelist: ['127.0.0.1', '::1'],
      timeout: 30000,
      max_payload_size: '10mb'
    },
    database_security: {
      ssl_mode: 'require',
      connection_timeout: 30000,
      max_connections: 20,
      idle_timeout: 300000
    },
    monitoring: {
      failed_login_threshold: 5,
      suspicious_activity_threshold: 10,
      alert_email: '<EMAIL>',
      log_retention_days: 90
    }
  };

  const configPath = path.join(__dirname, '..', 'secrets', 'security-config.json');
  
  // Create secrets directory if it doesn't exist
  const secretsDir = path.dirname(configPath);
  if (!fs.existsSync(secretsDir)) {
    fs.mkdirSync(secretsDir, { recursive: true, mode: 0o700 });
  }

  fs.writeFileSync(configPath, JSON.stringify(securityConfig, null, 2), { mode: 0o600 });
  log('✅ Güvenlik yapılandırması oluşturuldu: ' + configPath, 'green');
  
  return securityConfig;
}

function createEnvironmentFile(securityConfig) {
  log('📝 Güvenli environment dosyası oluşturuluyor...', 'blue');
  
  const envContent = `# n8n Trading System - Security Environment Variables
# Generated: ${new Date().toISOString()}
# Author: inkbytefo

# ================================
# n8n Core Security
# ================================
N8N_ENCRYPTION_KEY=${securityConfig.encryption.n8n_encryption_key}
N8N_USER_MANAGEMENT_DISABLED=false
N8N_SECURE_COOKIE=true
N8N_COOKIE_SAME_SITE=strict

# ================================
# Database Security
# ================================
DB_POSTGRESDB_SSL_ENABLED=true
DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED=true
DB_POSTGRESDB_CONNECTION_TIMEOUT=30000
DB_POSTGRESDB_IDLE_TIMEOUT=300000

# ================================
# Webhook Security
# ================================
WEBHOOK_SECRET=${securityConfig.encryption.webhook_secret}
WEBHOOK_SIGNATURE_VALIDATION=true
WEBHOOK_IP_WHITELIST=127.0.0.1,::1

# ================================
# API Security
# ================================
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=100
JWT_SECRET=${securityConfig.encryption.jwt_secret}

# ================================
# Redis Security
# ================================
REDIS_PASSWORD=${generateSecureKey(16)}
REDIS_TLS_ENABLED=false
REDIS_CONNECTION_TIMEOUT=10000

# ================================
# Monitoring & Logging
# ================================
SECURITY_MONITORING_ENABLED=true
FAILED_LOGIN_THRESHOLD=5
SUSPICIOUS_ACTIVITY_THRESHOLD=10
LOG_LEVEL=info
LOG_RETENTION_DAYS=90

# ================================
# External API Keys (Set these manually)
# ================================
OPENAI_API_KEY=your_openai_api_key_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here

# ================================
# Production Settings
# ================================
NODE_ENV=production
TZ=Europe/Istanbul
GENERIC_TIMEZONE=Europe/Istanbul
`;

  const envPath = path.join(__dirname, '..', '.env.production');
  fs.writeFileSync(envPath, envContent, { mode: 0o600 });
  log('✅ Environment dosyası oluşturuldu: ' + envPath, 'green');
}

function createSecurityMiddleware() {
  log('🛡️ Güvenlik middleware oluşturuluyor...', 'blue');
  
  const middlewareContent = `/**
 * Security Middleware for n8n Trading System
 * Author: inkbytefo
 * Description: Express middleware for enhanced security
 */

const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const crypto = require('crypto');

// Rate limiting configuration
const createRateLimiter = (windowMs = 900000, max = 100) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      error: 'Too many requests',
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/healthz';
    }
  });
};

// Webhook signature validation
const validateWebhookSignature = (secret) => {
  return (req, res, next) => {
    const signature = req.headers['x-webhook-signature'];
    const payload = JSON.stringify(req.body);
    
    if (!signature) {
      return res.status(401).json({ error: 'Missing webhook signature' });
    }
    
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
    
    const providedSignature = signature.replace('sha256=', '');
    
    if (!crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(providedSignature, 'hex')
    )) {
      return res.status(401).json({ error: 'Invalid webhook signature' });
    }
    
    next();
  };
};

// IP whitelist middleware
const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
      return res.status(403).json({ 
        error: 'IP not whitelisted',
        ip: clientIP 
      });
    }
    
    next();
  };
};

// Security headers
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.openai.com", "https://api.telegram.org"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// CORS configuration
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:5678'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Webhook-Signature']
};

module.exports = {
  createRateLimiter,
  validateWebhookSignature,
  ipWhitelist,
  securityHeaders,
  cors: cors(corsOptions)
};
`;

  const middlewarePath = path.join(__dirname, '..', 'middleware', 'security.js');
  
  // Create middleware directory if it doesn't exist
  const middlewareDir = path.dirname(middlewarePath);
  if (!fs.existsSync(middlewareDir)) {
    fs.mkdirSync(middlewareDir, { recursive: true });
  }

  fs.writeFileSync(middlewarePath, middlewareContent);
  log('✅ Güvenlik middleware oluşturuldu: ' + middlewarePath, 'green');
}

function createSecurityMonitoring() {
  log('📊 Güvenlik monitoring sistemi oluşturuluyor...', 'blue');
  
  const monitoringContent = `/**
 * Security Monitoring for n8n Trading System
 * Author: inkbytefo
 * Description: Real-time security monitoring and alerting
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');

class SecurityMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.failedAttempts = new Map();
    this.suspiciousActivities = new Map();
    this.config = {
      failedLoginThreshold: options.failedLoginThreshold || 5,
      suspiciousActivityThreshold: options.suspiciousActivityThreshold || 10,
      timeWindow: options.timeWindow || 900000, // 15 minutes
      logFile: options.logFile || path.join(__dirname, '..', 'logs', 'security.log')
    };
    
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.on('failed_login', this.handleFailedLogin.bind(this));
    this.on('suspicious_activity', this.handleSuspiciousActivity.bind(this));
    this.on('security_alert', this.handleSecurityAlert.bind(this));
  }

  logSecurityEvent(event, details) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details,
      severity: this.getSeverity(event)
    };

    // Write to log file
    const logLine = JSON.stringify(logEntry) + '\\n';
    fs.appendFileSync(this.config.logFile, logLine);

    // Emit event for real-time processing
    this.emit(event, details);
  }

  getSeverity(event) {
    const severityMap = {
      'failed_login': 'medium',
      'suspicious_activity': 'high',
      'security_breach': 'critical',
      'rate_limit_exceeded': 'medium',
      'invalid_webhook_signature': 'high'
    };
    
    return severityMap[event] || 'low';
  }

  handleFailedLogin(details) {
    const { ip, username } = details;
    const key = \`\${ip}:\${username}\`;
    
    if (!this.failedAttempts.has(key)) {
      this.failedAttempts.set(key, []);
    }
    
    const attempts = this.failedAttempts.get(key);
    attempts.push(Date.now());
    
    // Clean old attempts
    const cutoff = Date.now() - this.config.timeWindow;
    const recentAttempts = attempts.filter(time => time > cutoff);
    this.failedAttempts.set(key, recentAttempts);
    
    if (recentAttempts.length >= this.config.failedLoginThreshold) {
      this.emit('security_alert', {
        type: 'multiple_failed_logins',
        ip,
        username,
        attempts: recentAttempts.length,
        timeWindow: this.config.timeWindow
      });
    }
  }

  handleSuspiciousActivity(details) {
    const { ip, activity } = details;
    
    if (!this.suspiciousActivities.has(ip)) {
      this.suspiciousActivities.set(ip, []);
    }
    
    const activities = this.suspiciousActivities.get(ip);
    activities.push({ activity, timestamp: Date.now() });
    
    // Clean old activities
    const cutoff = Date.now() - this.config.timeWindow;
    const recentActivities = activities.filter(a => a.timestamp > cutoff);
    this.suspiciousActivities.set(ip, recentActivities);
    
    if (recentActivities.length >= this.config.suspiciousActivityThreshold) {
      this.emit('security_alert', {
        type: 'suspicious_activity_pattern',
        ip,
        activities: recentActivities,
        count: recentActivities.length
      });
    }
  }

  handleSecurityAlert(details) {
    console.error('🚨 SECURITY ALERT:', details);
    
    // Here you could integrate with external alerting systems
    // like Slack, email, or monitoring services
    
    // For now, just log to file with high priority
    this.logSecurityEvent('security_alert', details);
  }

  getSecurityReport() {
    return {
      failedAttempts: Object.fromEntries(this.failedAttempts),
      suspiciousActivities: Object.fromEntries(this.suspiciousActivities),
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = SecurityMonitor;
`;

  const monitoringPath = path.join(__dirname, '..', 'lib', 'security-monitor.js');
  
  // Create lib directory if it doesn't exist
  const libDir = path.dirname(monitoringPath);
  if (!fs.existsSync(libDir)) {
    fs.mkdirSync(libDir, { recursive: true });
  }

  fs.writeFileSync(monitoringPath, monitoringContent);
  log('✅ Güvenlik monitoring sistemi oluşturuldu: ' + monitoringPath, 'green');
}

function setupFilePermissions() {
  log('🔒 Dosya izinleri ayarlanıyor...', 'blue');
  
  try {
    // Set secure permissions for sensitive directories
    const sensitiveDirectories = [
      path.join(__dirname, '..', 'secrets'),
      path.join(__dirname, '..', 'logs'),
      path.join(__dirname, '..', 'backups')
    ];

    sensitiveDirectories.forEach(dir => {
      if (fs.existsSync(dir)) {
        execSync(\`chmod 700 "\${dir}"\`);
        log(\`✅ Dizin izinleri ayarlandı: \${dir}\`, 'green');
      }
    });

    // Set secure permissions for sensitive files
    const sensitiveFiles = [
      path.join(__dirname, '..', '.env.production'),
      path.join(__dirname, '..', 'secrets', 'security-config.json')
    ];

    sensitiveFiles.forEach(file => {
      if (fs.existsSync(file)) {
        execSync(\`chmod 600 "\${file}"\`);
        log(\`✅ Dosya izinleri ayarlandı: \${file}\`, 'green');
      }
    });

  } catch (error) {
    log(\`⚠️ Dosya izinleri ayarlanırken hata: \${error.message}\`, 'yellow');
  }
}

function main() {
  log('🚀 n8n Trading System güvenlik kurulumu başlatılıyor...', 'cyan');
  
  try {
    // Create necessary directories
    const directories = ['secrets', 'logs', 'middleware', 'lib'];
    directories.forEach(dir => {
      const dirPath = path.join(__dirname, '..', dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        log(\`📁 Dizin oluşturuldu: \${dirPath}\`, 'green');
      }
    });

    // Generate security configuration
    const securityConfig = createSecurityConfig();
    
    // Create environment file
    createEnvironmentFile(securityConfig);
    
    // Create security middleware
    createSecurityMiddleware();
    
    // Create security monitoring
    createSecurityMonitoring();
    
    // Setup file permissions
    setupFilePermissions();
    
    log('✅ Güvenlik kurulumu tamamlandı!', 'green');
    log('📋 Sonraki adımlar:', 'cyan');
    log('   1. .env.production dosyasındaki API anahtarlarını güncelleyin', 'yellow');
    log('   2. secrets/security-config.json dosyasını gözden geçirin', 'yellow');
    log('   3. Docker Compose ile sistemi yeniden başlatın', 'yellow');
    log('   4. Güvenlik loglarını izlemeye başlayın', 'yellow');
    
  } catch (error) {
    log(\`❌ Güvenlik kurulumu sırasında hata: \${error.message}\`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  createSecurityConfig,
  createEnvironmentFile,
  createSecurityMiddleware,
  createSecurityMonitoring,
  setupFilePermissions
};
`;

  const securitySetupPath = path.join(__dirname, 'security-setup.js');
  fs.writeFileSync(securitySetupPath, securitySetupContent, { mode: 0o755 });
  log('✅ Güvenlik kurulum scripti oluşturuldu: ' + securitySetupPath, 'green');
}

// Run the security setup
if (require.main === module) {
  const securityConfig = createSecurityConfig();
  createEnvironmentFile(securityConfig);
  createSecurityMiddleware();
  createSecurityMonitoring();
  setupFilePermissions();
  
  log('🎉 Güvenlik kurulumu tamamlandı!', 'green');
}
