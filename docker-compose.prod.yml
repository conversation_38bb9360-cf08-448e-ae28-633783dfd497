version: '3.8'

services:
  # PostgreSQL Database - Production Configuration
  postgres:
    image: postgres:15-alpine
    container_name: trading-postgres-prod
    restart: always
    environment:
      POSTGRES_DB: ${DB_POSTGRESDB_DATABASE}
      POSTGRES_USER: ${DB_POSTGRESDB_USER}
      POSTGRES_PASSWORD: ${DB_POSTGRESDB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./backups:/backups
    networks:
      - trading-network-prod
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_POSTGRESDB_USER} -d ${DB_POSTGRESDB_DATABASE}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # n8n Workflow Engine - Production Configuration
  n8n:
    image: n8nio/n8n:latest
    container_name: trading-n8n-prod
    restart: always
    environment:
      # Database Configuration
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: ${DB_POSTGRESDB_DATABASE}
      DB_POSTGRESDB_USER: ${DB_POSTGRESDB_USER}
      DB_POSTGRESDB_PASSWORD: ${DB_POSTGRESDB_PASSWORD}
      
      # n8n Configuration
      N8N_HOST: ${N8N_HOST}
      N8N_PORT: 5678
      N8N_PROTOCOL: https
      WEBHOOK_URL: https://${N8N_HOST}/
      
      # Security
      N8N_ENCRYPTION_KEY: ${N8N_ENCRYPTION_KEY}
      N8N_USER_MANAGEMENT_DISABLED: "false"
      N8N_BASIC_AUTH_ACTIVE: "true"
      N8N_BASIC_AUTH_USER: ${N8N_BASIC_AUTH_USER}
      N8N_BASIC_AUTH_PASSWORD: ${N8N_BASIC_AUTH_PASSWORD}
      
      # Performance
      EXECUTIONS_PROCESS: main
      EXECUTIONS_DATA_SAVE_ON_ERROR: all
      EXECUTIONS_DATA_SAVE_ON_SUCCESS: none
      EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS: true
      EXECUTIONS_DATA_PRUNE: "true"
      EXECUTIONS_DATA_MAX_AGE: 168
      
      # Logging
      N8N_LOG_LEVEL: warn
      N8N_LOG_OUTPUT: file
      
      # Timezone
      GENERIC_TIMEZONE: ${TZ:-UTC}
      TZ: ${TZ:-UTC}
    volumes:
      - n8n_data_prod:/home/<USER>/.n8n
      - ./workflows:/workflows:ro
      - ./logs/n8n:/home/<USER>/.n8n/logs
    networks:
      - trading-network-prod
    depends_on:
      postgres:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Trading System Application - Production Configuration
  trading-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trading-app-prod
    restart: always
    environment:
      # Database Configuration
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: ${DB_POSTGRESDB_DATABASE}
      DB_POSTGRESDB_USER: ${DB_POSTGRESDB_USER}
      DB_POSTGRESDB_PASSWORD: ${DB_POSTGRESDB_PASSWORD}
      
      # n8n Configuration
      N8N_HOST: n8n
      N8N_PORT: 5678
      N8N_PROTOCOL: http
      
      # API Keys (from secrets)
      OPENAI_API_KEY_FILE: /run/secrets/openai_api_key
      TELEGRAM_BOT_TOKEN_FILE: /run/secrets/telegram_bot_token
      
      # System Configuration
      NODE_ENV: production
      LOG_LEVEL: warn
      TZ: ${TZ:-UTC}
      
      # Trading Configuration
      DEFAULT_RISK_LEVEL: ${DEFAULT_RISK_LEVEL:-medium}
      MAX_POSITION_SIZE: ${MAX_POSITION_SIZE:-0.05}
      STOP_LOSS_PERCENTAGE: ${STOP_LOSS_PERCENTAGE:-3}
      TAKE_PROFIT_PERCENTAGE: ${TAKE_PROFIT_PERCENTAGE:-10}
      
      # Security
      WEBHOOK_SECRET: ${WEBHOOK_SECRET}
      API_RATE_LIMIT: ${API_RATE_LIMIT:-50}
    volumes:
      - app_logs_prod:/app/logs
      - app_backups_prod:/app/backups
    networks:
      - trading-network-prod
    depends_on:
      postgres:
        condition: service_healthy
      n8n:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    secrets:
      - openai_api_key
      - telegram_bot_token
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: trading-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - trading-network-prod
    depends_on:
      - n8n
      - trading-app
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis for Caching - Production Configuration
  redis:
    image: redis:7-alpine
    container_name: trading-redis-prod
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data_prod:/data
    networks:
      - trading-network-prod
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

secrets:
  openai_api_key:
    file: ./secrets/openai_api_key.txt
  telegram_bot_token:
    file: ./secrets/telegram_bot_token.txt

volumes:
  postgres_data_prod:
    driver: local
  n8n_data_prod:
    driver: local
  app_logs_prod:
    driver: local
  app_backups_prod:
    driver: local
  redis_data_prod:
    driver: local

networks:
  trading-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
