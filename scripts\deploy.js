#!/usr/bin/env node

/**
 * Production Deployment Script
 * Author: inkbytefo
 * Description: Automated deployment script for Multi-Agent AI Trading System
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Color functions for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkPrerequisites() {
  log('🔍 Deployment prerequisites kontrol ediliyor...', 'blue');
  
  // Docker kontrolü
  try {
    execSync('docker --version', { stdio: 'pipe' });
    log('✅ Docker kurulu', 'green');
  } catch (error) {
    log('❌ Docker bulunamadı', 'red');
    process.exit(1);
  }
  
  // Docker Compose kontrolü
  try {
    execSync('docker compose version', { stdio: 'pipe' });
    log('✅ Docker Compose kurulu', 'green');
  } catch (error) {
    log('❌ Docker Compose bulunamadı', 'red');
    process.exit(1);
  }
  
  // .env dosyası kontrolü
  if (!fs.existsSync('.env')) {
    log('❌ .env dosyası bulunamadı', 'red');
    log('🔧 Önce .env dosyasını oluşturun', 'yellow');
    process.exit(1);
  }
  
  // Secrets kontrolü
  const requiredSecrets = ['openai_api_key.txt', 'telegram_bot_token.txt'];
  const missingSecrets = requiredSecrets.filter(secret => 
    !fs.existsSync(path.join('secrets', secret))
  );
  
  if (missingSecrets.length > 0) {
    log(`❌ Eksik secret dosyaları: ${missingSecrets.join(', ')}`, 'red');
    log('🔧 secrets/ klasöründe gerekli dosyaları oluşturun', 'yellow');
    process.exit(1);
  }
  
  log('✅ Tüm prerequisites tamam', 'green');
}

function buildImages() {
  log('\n🏗️  Docker images build ediliyor...', 'blue');
  
  try {
    // Production image build
    execSync('docker compose -f docker-compose.prod.yml build --no-cache trading-app', {
      stdio: 'inherit'
    });
    log('✅ Trading app image build edildi', 'green');
    
    // Pull other images
    execSync('docker compose -f docker-compose.prod.yml pull', {
      stdio: 'inherit'
    });
    log('✅ Diğer images pull edildi', 'green');
    
  } catch (error) {
    log('❌ Image build hatası', 'red');
    process.exit(1);
  }
}

function runSecurityScan() {
  log('\n🔒 Güvenlik taraması yapılıyor...', 'blue');
  
  try {
    // Docker image security scan (if available)
    try {
      execSync('docker scout cves trading-app-prod:latest', { stdio: 'pipe' });
      log('✅ Güvenlik taraması tamamlandı', 'green');
    } catch (error) {
      log('⚠️  Docker Scout bulunamadı, güvenlik taraması atlandı', 'yellow');
    }
    
    // Check for secrets in image
    const imageInspect = execSync('docker inspect trading-app-prod:latest', { encoding: 'utf8' });
    if (imageInspect.includes('API_KEY') || imageInspect.includes('TOKEN')) {
      log('⚠️  Image içinde potansiyel secret bulundu', 'yellow');
    }
    
  } catch (error) {
    log('⚠️  Güvenlik taraması tamamlanamadı', 'yellow');
  }
}

function deployServices() {
  log('\n🚀 Services deploy ediliyor...', 'blue');
  
  try {
    // Stop existing services
    try {
      execSync('docker compose -f docker-compose.prod.yml down', { stdio: 'pipe' });
    } catch (error) {
      // Services zaten durmuş olabilir
    }
    
    // Start services
    execSync('docker compose -f docker-compose.prod.yml up -d', {
      stdio: 'inherit'
    });
    
    log('✅ Services başlatıldı', 'green');
    
  } catch (error) {
    log('❌ Deployment hatası', 'red');
    process.exit(1);
  }
}

function waitForServices() {
  log('\n⏳ Services hazır olması bekleniyor...', 'blue');
  
  const services = ['postgres', 'n8n', 'trading-app'];
  const maxAttempts = 30;
  
  for (const service of services) {
    let attempts = 0;
    let healthy = false;
    
    while (attempts < maxAttempts && !healthy) {
      try {
        const result = execSync(
          `docker compose -f docker-compose.prod.yml ps --format json ${service}`,
          { encoding: 'utf8', stdio: 'pipe' }
        );
        
        const serviceInfo = JSON.parse(result);
        if (serviceInfo.Health === 'healthy' || serviceInfo.State === 'running') {
          healthy = true;
          log(`✅ ${service} hazır`, 'green');
        }
      } catch (error) {
        // Service henüz hazır değil
      }
      
      if (!healthy) {
        attempts++;
        execSync('sleep 2');
      }
    }
    
    if (!healthy) {
      log(`❌ ${service} timeout`, 'red');
      process.exit(1);
    }
  }
}

function runHealthChecks() {
  log('\n🏥 Health check\'ler çalıştırılıyor...', 'blue');
  
  const healthChecks = [
    {
      name: 'PostgreSQL',
      command: 'docker compose -f docker-compose.prod.yml exec -T postgres pg_isready -U n8n_user'
    },
    {
      name: 'n8n',
      command: 'curl -f http://localhost:5678/healthz'
    },
    {
      name: 'Trading App',
      command: 'curl -f http://localhost:3000/health'
    }
  ];
  
  for (const check of healthChecks) {
    try {
      execSync(check.command, { stdio: 'pipe' });
      log(`✅ ${check.name} health check OK`, 'green');
    } catch (error) {
      log(`❌ ${check.name} health check FAIL`, 'red');
    }
  }
}

function setupMonitoring() {
  log('\n📊 Monitoring kurulumu...', 'blue');
  
  try {
    // Create monitoring script
    const monitoringScript = `#!/bin/bash
# Monitoring script for trading system

while true; do
  echo "$(date): Checking system health..."
  
  # Check container status
  docker compose -f docker-compose.prod.yml ps
  
  # Check resource usage
  docker stats --no-stream --format "table {{.Container}}\\t{{.CPUPerc}}\\t{{.MemUsage}}"
  
  # Check logs for errors
  docker compose -f docker-compose.prod.yml logs --tail=10 trading-app | grep -i error
  
  sleep 300  # 5 minutes
done
`;
    
    fs.writeFileSync('scripts/monitor.sh', monitoringScript);
    execSync('chmod +x scripts/monitor.sh');
    
    log('✅ Monitoring script oluşturuldu', 'green');
    
  } catch (error) {
    log('⚠️  Monitoring kurulumu başarısız', 'yellow');
  }
}

function displayDeploymentInfo() {
  log('\n🎉 Deployment tamamlandı!', 'green');
  log('==========================\n', 'green');
  
  log('📋 Service URLs:', 'cyan');
  log('• n8n: https://your-domain.com', 'yellow');
  log('• Trading App API: https://your-domain.com/api', 'yellow');
  log('• Health Check: https://your-domain.com/health', 'yellow');
  
  log('\n📊 Monitoring:', 'cyan');
  log('• Logs: docker compose -f docker-compose.prod.yml logs -f', 'yellow');
  log('• Stats: docker stats', 'yellow');
  log('• Monitor: ./scripts/monitor.sh', 'yellow');
  
  log('\n🔧 Management:', 'cyan');
  log('• Stop: docker compose -f docker-compose.prod.yml down', 'yellow');
  log('• Restart: docker compose -f docker-compose.prod.yml restart', 'yellow');
  log('• Update: ./scripts/deploy.js --update', 'yellow');
  
  log('\n⚠️  Sonraki Adımlar:', 'cyan');
  log('1. SSL sertifikalarını nginx/ssl/ klasörüne yerleştirin', 'yellow');
  log('2. Domain DNS ayarlarını yapılandırın', 'yellow');
  log('3. n8n\'de workflow\'ları import edin', 'yellow');
  log('4. Monitoring ve alerting kurun', 'yellow');
}

// Ana deployment fonksiyonu
async function main() {
  const args = process.argv.slice(2);
  const isUpdate = args.includes('--update');
  
  log('🚀 Multi-Agent AI Trading System - Production Deployment', 'magenta');
  log('=========================================================\n', 'magenta');
  
  try {
    checkPrerequisites();
    
    if (!isUpdate) {
      buildImages();
      runSecurityScan();
    }
    
    deployServices();
    waitForServices();
    runHealthChecks();
    
    if (!isUpdate) {
      setupMonitoring();
    }
    
    displayDeploymentInfo();
    
  } catch (error) {
    log(`\n❌ Deployment hatası: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Script'i çalıştır
if (require.main === module) {
  main();
}
