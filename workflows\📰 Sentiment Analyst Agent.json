{"name": "📰 Sentiment Analyst Agent", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 10}]}}, "id": "schedule-trigger", "name": "Every 10 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [112, 208], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM market_data WHERE source = 'data_harvester' AND timestamp > NOW() - INTERVAL '2 hours' ORDER BY timestamp DESC LIMIT 20", "options": {}}, "id": "get-news-data", "name": "Get Latest News Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [304, 208], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "Sen bir kripto para duygu analizi uzmanısın. Aşağıdaki haber ve sosyal medya verilerini analiz et:\n\n{{JSON.stringify($json, null, 2)}}\n\nGörevlerin:\n1. <PERSON><PERSON> piyasa duygu skorunu belirle (-1 ile +1 arası)\n2. <PERSON>htar haberleri özetle\n3. Piyasa etkisi yüksek haberleri tespit et\n4. FOMO/FUD seviyesini ölç\n5. Sosyal medya trendlerini analiz et\n6. Duygu değişimlerini takip et\n\nJSON formatında duygu analizi raporu sun.", "hasOutputParser": true, "options": {}}, "id": "ai-sentiment-analyst", "name": "AI Sentiment Analysis Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [400, 64], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// AI Sentiment Analysis Agent çıktısını database için hazırla\nconst sentimentOutput = $input.first().json;\n\n// Güvenli string escape fonksiyonu\nfunction escapeString(str) {\n  if (typeof str !== 'string') return str;\n  return str.replace(/'/g, \"''\");\n}\n\n// Database için veri hazırla\nconst dbData = {\n  overall_sentiment_score: sentimentOutput.overall_sentiment_score || 0,\n  sentiment_trend: sentimentOutput.sentiment_trend || 'NEUTRAL',\n  market_mood: sentimentOutput.market_mood || 'NEUTRAL',\n  fomo_fud_level: sentimentOutput.fomo_fud_level || 'NORMAL',\n  confidence_level: sentimentOutput.confidence_level || 0.5,\n  analysis_data_escaped: escapeString(JSON.stringify(sentimentOutput))\n};\n\nreturn { json: dbData };"}, "id": "prepare-sentiment-data", "name": "Prepare Sentiment Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [624, 64], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO sentiment_analysis (overall_sentiment_score, sentiment_trend, market_mood, fomo_fud_level, confidence_level, analysis_data, timestamp) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)", "options": {}}, "id": "save-sentiment-analysis", "name": "Save Sentiment Analysis", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [800, 160], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "extreme-positive", "leftValue": "={{ Math.abs($('Prepare Sentiment Data').first().json.overall_sentiment_score) }}", "rightValue": 0.7, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "check-extreme-sentiment", "name": "Check Extreme Sentiment", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [480, 352], "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/master-strategist", "sendBody": true, "bodyParameters": {"parameters": [{"name": "alert_type", "value": "extreme_sentiment_detected"}, {"name": "source_agent", "value": "sentiment_analyst"}, {"name": "sentiment_data", "value": "={{ JSON.stringify($('AI Sentiment Analysis Agent').first().json) }}"}, {"name": "urgency", "value": "HIGH"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "trigger-strategist", "name": "Trigger Master Strategist", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [848, 304], "onError": "continueRegularOutput"}], "pinData": {}, "connections": {"Every 10 Minutes": {"main": [[{"node": "Get Latest News Data", "type": "main", "index": 0}]]}, "Get Latest News Data": {"main": [[{"node": "AI Sentiment Analysis Agent", "type": "main", "index": 0}]]}, "AI Sentiment Analysis Agent": {"main": [[{"node": "Prepare Sentiment Data", "type": "main", "index": 0}]]}, "Prepare Sentiment Data": {"main": [[{"node": "Save Sentiment Analysis", "type": "main", "index": 0}, {"node": "Check Extreme Sentiment", "type": "main", "index": 0}]]}, "Check Extreme Sentiment": {"main": [[{"node": "Trigger Master Strategist", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": true, "saveExecutionProgress": true}, "versionId": "9df7bc7d-75a0-4dba-927c-2e443b35f88c", "meta": {"instanceId": "a714df1aa11da563210a0570d83019f8d8c6597fc9f1fe509b3525f7215ea45e"}, "id": "i256USwIaeMbbhlQ", "tags": []}