-- Database Performance Optimizations
-- Author: inkbytefo
-- Description: Advanced indexing and performance tuning for trading system

-- ================================
-- Advanced Indexing Strategy
-- ================================

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_symbol_timestamp_quality 
ON market_data (symbol, timestamp DESC, ai_quality_score DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_technical_analysis_symbol_confidence 
ON technical_analysis (symbol, confidence_score DESC, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sentiment_analysis_timestamp_confidence 
ON sentiment_analysis (timestamp DESC, confidence_level DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_risk_assessments_timestamp_risk 
ON risk_assessments (timestamp DESC, overall_risk_level, portfolio_risk_score DESC);

-- JSONB indexes for analysis data
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_analysis_gin 
ON market_data USING GIN (ai_analysis);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_technical_analysis_data_gin 
ON technical_analysis USING GIN (analysis_data);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sentiment_analysis_data_gin 
ON sentiment_analysis USING GIN (analysis_data);

-- Partial indexes for active data
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_recent_high_quality 
ON market_data (symbol, timestamp DESC) 
WHERE timestamp > NOW() - INTERVAL '24 hours' AND ai_quality_score > 0.7;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_technical_analysis_recent_confident 
ON technical_analysis (symbol, timestamp DESC) 
WHERE timestamp > NOW() - INTERVAL '24 hours' AND confidence_score > 0.8;

-- ================================
-- Performance Views
-- ================================

-- Real-time market overview
CREATE OR REPLACE VIEW v_market_overview AS
SELECT 
    md.symbol,
    md.price as current_price,
    md.price_change_percentage_24h,
    md.volume_24h,
    md.ai_quality_score,
    ta.trend_direction,
    ta.buy_sell_signal,
    ta.confidence_score as technical_confidence,
    sa.overall_sentiment_score,
    sa.market_mood,
    ra.overall_risk_level,
    ra.portfolio_risk_score,
    md.timestamp as last_updated
FROM market_data md
LEFT JOIN LATERAL (
    SELECT * FROM technical_analysis 
    WHERE symbol = md.symbol 
    ORDER BY timestamp DESC 
    LIMIT 1
) ta ON true
LEFT JOIN LATERAL (
    SELECT * FROM sentiment_analysis 
    ORDER BY timestamp DESC 
    LIMIT 1
) sa ON true
LEFT JOIN LATERAL (
    SELECT * FROM risk_assessments 
    ORDER BY timestamp DESC 
    LIMIT 1
) ra ON true
WHERE md.timestamp > NOW() - INTERVAL '1 hour'
ORDER BY md.ai_quality_score DESC, md.timestamp DESC;

-- High-confidence signals view
CREATE OR REPLACE VIEW v_high_confidence_signals AS
SELECT 
    ta.symbol,
    ta.buy_sell_signal,
    ta.confidence_score,
    ta.trend_direction,
    ta.next_price_target,
    ta.stop_loss_suggestion,
    md.price as current_price,
    md.ai_quality_score,
    ta.timestamp
FROM technical_analysis ta
JOIN market_data md ON ta.symbol = md.symbol
WHERE ta.confidence_score > 0.85
    AND ta.timestamp > NOW() - INTERVAL '2 hours'
    AND md.timestamp > NOW() - INTERVAL '30 minutes'
    AND md.ai_quality_score > 0.8
ORDER BY ta.confidence_score DESC, ta.timestamp DESC;

-- Risk monitoring view
CREATE OR REPLACE VIEW v_risk_monitoring AS
SELECT 
    ra.overall_risk_level,
    ra.portfolio_risk_score,
    ra.volatility_level,
    ra.max_daily_loss_limit,
    ra.portfolio_value,
    ra.open_positions,
    ra.confidence_level,
    COUNT(ta.id) as active_signals,
    AVG(ta.confidence_score) as avg_signal_confidence,
    ra.timestamp
FROM risk_assessments ra
LEFT JOIN technical_analysis ta ON ta.timestamp > ra.timestamp - INTERVAL '1 hour'
    AND ta.confidence_score > 0.7
WHERE ra.timestamp > NOW() - INTERVAL '4 hours'
GROUP BY ra.id, ra.overall_risk_level, ra.portfolio_risk_score, 
         ra.volatility_level, ra.max_daily_loss_limit, ra.portfolio_value,
         ra.open_positions, ra.confidence_level, ra.timestamp
ORDER BY ra.timestamp DESC;

-- ================================
-- Performance Functions
-- ================================

-- Function to get latest market data with quality filtering
CREATE OR REPLACE FUNCTION get_latest_market_data(
    p_symbol VARCHAR DEFAULT NULL,
    p_min_quality DECIMAL DEFAULT 0.5,
    p_limit INTEGER DEFAULT 100
)
RETURNS TABLE (
    symbol VARCHAR,
    price DECIMAL,
    volume_24h DECIMAL,
    price_change_percentage_24h DECIMAL,
    ai_quality_score DECIMAL,
    timestamp TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        md.symbol,
        md.price,
        md.volume_24h,
        md.price_change_percentage_24h,
        md.ai_quality_score,
        md.timestamp
    FROM market_data md
    WHERE (p_symbol IS NULL OR md.symbol = p_symbol)
        AND md.ai_quality_score >= p_min_quality
        AND md.timestamp > NOW() - INTERVAL '2 hours'
    ORDER BY md.timestamp DESC, md.ai_quality_score DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate technical indicator aggregates
CREATE OR REPLACE FUNCTION get_technical_summary(
    p_symbol VARCHAR,
    p_hours INTEGER DEFAULT 24
)
RETURNS TABLE (
    symbol VARCHAR,
    avg_confidence DECIMAL,
    dominant_trend VARCHAR,
    signal_count INTEGER,
    buy_signals INTEGER,
    sell_signals INTEGER,
    avg_rsi DECIMAL,
    latest_signal VARCHAR,
    latest_confidence DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ta.symbol,
        AVG(ta.confidence_score) as avg_confidence,
        MODE() WITHIN GROUP (ORDER BY ta.trend_direction) as dominant_trend,
        COUNT(*)::INTEGER as signal_count,
        COUNT(CASE WHEN ta.buy_sell_signal = 'BUY' THEN 1 END)::INTEGER as buy_signals,
        COUNT(CASE WHEN ta.buy_sell_signal = 'SELL' THEN 1 END)::INTEGER as sell_signals,
        AVG(ta.rsi_value) as avg_rsi,
        (SELECT buy_sell_signal FROM technical_analysis 
         WHERE symbol = p_symbol ORDER BY timestamp DESC LIMIT 1) as latest_signal,
        (SELECT confidence_score FROM technical_analysis 
         WHERE symbol = p_symbol ORDER BY timestamp DESC LIMIT 1) as latest_confidence
    FROM technical_analysis ta
    WHERE ta.symbol = p_symbol
        AND ta.timestamp > NOW() - (p_hours || ' hours')::INTERVAL
    GROUP BY ta.symbol;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- Maintenance Procedures
-- ================================

-- Procedure to update table statistics
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS VOID AS $$
BEGIN
    ANALYZE market_data;
    ANALYZE technical_analysis;
    ANALYZE sentiment_analysis;
    ANALYZE risk_assessments;
    ANALYZE synthesis_reports;
    
    INSERT INTO system_logs (log_level, message, component, timestamp)
    VALUES ('INFO', 'Table statistics updated successfully', 'maintenance', NOW());
END;
$$ LANGUAGE plpgsql;

-- Schedule statistics update (requires pg_cron extension)
-- SELECT cron.schedule('update-stats', '0 */6 * * *', 'SELECT update_table_statistics();');

-- Grant permissions to n8n user
GRANT EXECUTE ON FUNCTION get_latest_market_data TO n8n_user;
GRANT EXECUTE ON FUNCTION get_technical_summary TO n8n_user;
GRANT EXECUTE ON FUNCTION update_table_statistics TO n8n_user;
GRANT SELECT ON v_market_overview TO n8n_user;
GRANT SELECT ON v_high_confidence_signals TO n8n_user;
GRANT SELECT ON v_risk_monitoring TO n8n_user;

-- Log successful optimization
INSERT INTO system_logs (log_level, message, component, timestamp)
VALUES ('INFO', 'Database optimizations applied successfully', 'database', NOW());
