-- Multi-Agent AI Trading System Database Schema
-- PostgreSQL 12+ Required

-- Create database (run as superuser)
-- CREATE DATABASE n8n_trading;
-- CREATE USER n8n_user WITH PASSWORD 'your_secure_password';
-- GRANT ALL PRIVILEGES ON DATABASE n8n_trading TO n8n_user;

-- Connect to n8n_trading database
\c n8n_trading;

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Market Data Table
CREATE TABLE market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    price DECIMAL(20, 8),
    volume_24h DECIMAL(20, 8),
    price_change_percentage_24h DECIMAL(10, 4),
    market_cap DECIMAL(20, 2),
    source VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ai_quality_score DECIMAL(3, 2),
    ai_analysis JSONB,
    data_reliability VARCHAR(10) DEFAULT 'UNKNOWN',
    weight_factor DECIMAL(3, 2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Technical Analysis Table
CREATE TABLE technical_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    trend_direction VARCHAR(20),
    market_phase VARCHAR(20),
    buy_sell_signal VARCHAR(10),
    confidence_score DECIMAL(3, 2),
    risk_level VARCHAR(10),
    rsi_value DECIMAL(5, 2),
    macd_signal VARCHAR(50),
    support_level DECIMAL(20, 8),
    resistance_level DECIMAL(20, 8),
    next_price_target DECIMAL(20, 8),
    stop_loss_suggestion DECIMAL(20, 8),
    analysis_data JSONB,
    data_quality_score DECIMAL(3, 2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Sentiment Analysis Table
CREATE TABLE sentiment_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    overall_sentiment_score DECIMAL(3, 2),
    sentiment_trend VARCHAR(20),
    market_mood VARCHAR(20),
    fomo_fud_level VARCHAR(10),
    confidence_level DECIMAL(3, 2),
    analysis_data JSONB,
    news_count INTEGER DEFAULT 0,
    social_media_mentions INTEGER DEFAULT 0,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Risk Assessments Table
CREATE TABLE risk_assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_risk_score DECIMAL(3, 2),
    volatility_level VARCHAR(10),
    overall_risk_level VARCHAR(10),
    max_daily_loss_limit DECIMAL(10, 2),
    confidence_level DECIMAL(3, 2),
    assessment_data JSONB,
    portfolio_value DECIMAL(20, 2),
    open_positions INTEGER DEFAULT 0,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Synthesis Reports Table (Master Strategist)
CREATE TABLE synthesis_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_id VARCHAR(50) UNIQUE NOT NULL,
    market_scenario TEXT,
    confidence_score DECIMAL(3, 2),
    primary_recommendation JSONB,
    validation_score DECIMAL(3, 2),
    strategy_approved BOOLEAN DEFAULT FALSE,
    raw_analysis_data JSONB,
    execution_status VARCHAR(20) DEFAULT 'PENDING',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Actionable Insights Table
CREATE TABLE actionable_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_id VARCHAR(50) REFERENCES synthesis_reports(strategy_id),
    action_type VARCHAR(20) NOT NULL,
    symbols JSONB,
    target_price DECIMAL(20, 8),
    stop_loss DECIMAL(20, 8),
    position_size DECIMAL(10, 4),
    confidence_level DECIMAL(3, 2),
    reasoning TEXT,
    execution_timeline VARCHAR(50),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    executed_at TIMESTAMP WITH TIME ZONE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- System Logs Table
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_level VARCHAR(10) NOT NULL,
    source VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    error_code VARCHAR(20),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- API Error Logs Table
CREATE TABLE api_error_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_source VARCHAR(50) NOT NULL,
    error_type VARCHAR(50),
    error_message TEXT,
    http_status_code INTEGER,
    retry_count INTEGER DEFAULT 0,
    resolved BOOLEAN DEFAULT FALSE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Performance Metrics Table
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(50) NOT NULL,
    metric_value DECIMAL(20, 8),
    metric_unit VARCHAR(20),
    agent_source VARCHAR(50),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create Indexes for Performance
CREATE INDEX idx_market_data_timestamp ON market_data(timestamp DESC);
CREATE INDEX idx_market_data_symbol ON market_data(symbol);
CREATE INDEX idx_market_data_source ON market_data(source);
CREATE INDEX idx_market_data_quality ON market_data(ai_quality_score DESC);

CREATE INDEX idx_technical_analysis_timestamp ON technical_analysis(timestamp DESC);
CREATE INDEX idx_technical_analysis_symbol ON technical_analysis(symbol);
CREATE INDEX idx_technical_analysis_confidence ON technical_analysis(confidence_score DESC);

CREATE INDEX idx_sentiment_analysis_timestamp ON sentiment_analysis(timestamp DESC);
CREATE INDEX idx_sentiment_analysis_score ON sentiment_analysis(overall_sentiment_score);

CREATE INDEX idx_risk_assessments_timestamp ON risk_assessments(timestamp DESC);
CREATE INDEX idx_risk_assessments_level ON risk_assessments(overall_risk_level);

CREATE INDEX idx_synthesis_reports_timestamp ON synthesis_reports(timestamp DESC);
CREATE INDEX idx_synthesis_reports_approved ON synthesis_reports(strategy_approved);

CREATE INDEX idx_actionable_insights_timestamp ON actionable_insights(timestamp DESC);
CREATE INDEX idx_actionable_insights_status ON actionable_insights(status);

CREATE INDEX idx_system_logs_timestamp ON system_logs(timestamp DESC);
CREATE INDEX idx_system_logs_level ON system_logs(log_level);
CREATE INDEX idx_system_logs_source ON system_logs(source);

-- Create Views for Easy Querying
CREATE VIEW latest_market_data AS
SELECT DISTINCT ON (symbol) *
FROM market_data
ORDER BY symbol, timestamp DESC;

CREATE VIEW high_confidence_signals AS
SELECT ta.*, md.price as current_price
FROM technical_analysis ta
JOIN latest_market_data md ON ta.symbol = md.symbol
WHERE ta.confidence_score >= 0.8
ORDER BY ta.timestamp DESC;

CREATE VIEW active_strategies AS
SELECT sr.*, ai.action_type, ai.symbols, ai.target_price
FROM synthesis_reports sr
JOIN actionable_insights ai ON sr.strategy_id = ai.strategy_id
WHERE sr.strategy_approved = TRUE AND ai.status = 'ACTIVE'
ORDER BY sr.timestamp DESC;

-- Grant permissions to n8n_user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO n8n_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO n8n_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO n8n_user;
