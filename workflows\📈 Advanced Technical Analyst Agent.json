{"name": "📈 Advanced Technical Analyst Agent", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "id": "schedule-trigger", "name": "Every 5 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-80, 288], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT symbol, price, volume_24h, price_change_percentage_24h, timestamp FROM market_data WHERE timestamp > NOW() - INTERVAL '4 hours' ORDER BY timestamp DESC LIMIT 100", "options": {}}, "id": "get-market-data", "name": "Get Latest Market Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [192, 288], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Technical Indicators Calculator - <PERSON><PERSON> Matematiksel Hesaplamalar\nconst marketData = $input.all();\nconst results = [];\n\n// Veriyi sembol bazında grupla\nconst symbolGroups = {};\nmarketData.forEach(item => {\n  const symbol = item.json.symbol || 'UNKNOWN';\n  if (!symbolGroups[symbol]) symbolGroups[symbol] = [];\n  symbolGroups[symbol].push({\n    price: parseFloat(item.json.price) || 0,\n    volume: parseFloat(item.json.volume_24h) || 0,\n    timestamp: item.json.timestamp\n  });\n});\n\n// Her sembol için hesaplama\nfor (const [symbol, data] of Object.entries(symbolGroups)) {\n  // Fiyatları zaman sırasına göre sırala\n  data.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));\n  const prices = data.map(d => d.price).filter(p => p > 0);\n  const volumes = data.map(d => d.volume).filter(v => v > 0);\n  \n  if (prices.length < 14) {\n    results.push({\n      symbol: symbol,\n      error: 'INSUFFICIENT_DATA',\n      data_points: prices.length,\n      required_minimum: 14\n    });\n    continue;\n  }\n  \n  // RSI Hesaplama (14 period)\n  function calculateRSI(prices, period = 14) {\n    let gains = 0, losses = 0;\n    \n    for (let i = 1; i <= period && i < prices.length; i++) {\n      const change = prices[i] - prices[i-1];\n      if (change > 0) gains += change;\n      else losses += Math.abs(change);\n    }\n    \n    const avgGain = gains / period;\n    const avgLoss = losses / period;\n    \n    if (avgLoss === 0) return 100;\n    const rs = avgGain / avgLoss;\n    return 100 - (100 / (1 + rs));\n  }\n  \n  // SMA Hesaplama\n  function calculateSMA(values, period) {\n    if (values.length < period) return values.reduce((a, b) => a + b, 0) / values.length;\n    const recentValues = values.slice(-period);\n    return recentValues.reduce((a, b) => a + b, 0) / period;\n  }\n  \n  // EMA Hesaplama\n  function calculateEMA(values, period) {\n    if (values.length < period) return calculateSMA(values, period);\n    \n    const multiplier = 2 / (period + 1);\n    let ema = calculateSMA(values.slice(0, period), period);\n    \n    for (let i = period; i < values.length; i++) {\n      ema = (values[i] * multiplier) + (ema * (1 - multiplier));\n    }\n    return ema;\n  }\n  \n  // Bollinger Bands\n  function calculateBollingerBands(prices, period = 20, stdDev = 2) {\n    const sma = calculateSMA(prices, period);\n    if (prices.length < period) return { upper: sma * 1.02, middle: sma, lower: sma * 0.98 };\n    \n    const recentPrices = prices.slice(-period);\n    const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;\n    const standardDeviation = Math.sqrt(variance);\n    \n    return {\n      upper: sma + (standardDeviation * stdDev),\n      middle: sma,\n      lower: sma - (standardDeviation * stdDev)\n    };\n  }\n  \n  // Hesaplamalar\n  const currentPrice = prices[prices.length - 1];\n  const rsi = calculateRSI(prices);\n  const sma20 = calculateSMA(prices, 20);\n  const sma50 = calculateSMA(prices, Math.min(50, prices.length));\n  const ema12 = calculateEMA(prices, 12);\n  const ema26 = calculateEMA(prices, 26);\n  const macd = ema12 - ema26;\n  const macdSignal = calculateEMA([macd], 9);\n  const macdHistogram = macd - macdSignal;\n  const bollinger = calculateBollingerBands(prices);\n  \n  // Volatilite (ATR benzeri)\n  const priceChanges = [];\n  for (let i = 1; i < Math.min(prices.length, 14); i++) {\n    priceChanges.push(Math.abs((prices[prices.length - i] - prices[prices.length - i - 1]) / prices[prices.length - i - 1]));\n  }\n  const volatility = (priceChanges.reduce((a, b) => a + b, 0) / priceChanges.length) * 100;\n  \n  // Destek ve Direnç (basit)\n  const recentPrices = prices.slice(-20);\n  const support = Math.min(...recentPrices);\n  const resistance = Math.max(...recentPrices);\n  \n  // Volume analizi\n  const volumeSMA = calculateSMA(volumes, Math.min(20, volumes.length));\n  const currentVolume = volumes[volumes.length - 1] || 0;\n  \n  results.push({\n    symbol: symbol,\n    current_price: Math.round(currentPrice * 100000) / 100000,\n    rsi: Math.round(rsi * 100) / 100,\n    macd: Math.round(macd * 100000) / 100000,\n    macd_signal: Math.round(macdSignal * 100000) / 100000,\n    macd_histogram: Math.round(macdHistogram * 100000) / 100000,\n    bollinger_upper: Math.round(bollinger.upper * 100000) / 100000,\n    bollinger_middle: Math.round(bollinger.middle * 100000) / 100000,\n    bollinger_lower: Math.round(bollinger.lower * 100000) / 100000,\n    sma_20: Math.round(sma20 * 100000) / 100000,\n    sma_50: Math.round(sma50 * 100000) / 100000,\n    ema_12: Math.round(ema12 * 100000) / 100000,\n    ema_26: Math.round(ema26 * 100000) / 100000,\n    volatility_percentage: Math.round(volatility * 100) / 100,\n    support_level: Math.round(support * 100000) / 100000,\n    resistance_level: Math.round(resistance * 100000) / 100000,\n    volume_sma: Math.round(volumeSMA),\n    current_volume: Math.round(currentVolume),\n    volume_ratio: currentVolume > 0 && volumeSMA > 0 ? Math.round((currentVolume / volumeSMA) * 100) / 100 : 1,\n    data_points_used: prices.length,\n    calculation_timestamp: new Date().toISOString(),\n    price_change_24h: prices.length >= 24 ? Math.round(((currentPrice - prices[prices.length - 24]) / prices[prices.length - 24]) * 10000) / 100 : 0\n  });\n}\n\nreturn results.map(result => ({ json: result }));"}, "id": "technical-calculations", "name": "Calculate Technical Indicators", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [416, 288], "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "Sen bir elit kripto para teknik analiz uzmanısın. Aşağıdaki KESIN matematiksel göstergeler hesaplandı:\n\n{{JSON.stringify($json, null, 2)}}\n\nGörevlerin:\n1. Bu göstergeleri profesyonelce yorumla\n2. <PERSON>rend y<PERSON><PERSON>ü<PERSON><PERSON> belirle (BULLISH/BEARISH/SIDEWAYS)\n3. Bu bir alım fırsatı mı, yoksa bir boğa tuzağı mı?\n4. RSI aşırı alım/satım seviyelerini değerlendir (>70 aşırı alım, <30 aşırı satım)\n5. MACD sinyallerini analiz et (histogram pozitif/negatif, sinyal çizgisi kesişimi)\n6. Bollinger Bantları pozisyonunu yorumla (fiyat hangi bantta?)\n7. Volatilite seviyesini değerlendir (>5% yüksek volatilite)\n8. Volume analizini yap (volume_ratio >1.5 yüksek hacim)\n9. Net AL/SAT/BEKLE kararı ver\n10. <PERSON><PERSON><PERSON> skorunu belirle (0-1 arası)\n11. Risk seviyesini hesapla\n\nJSON formatında profesyonel teknik analiz raporu sun.", "hasOutputParser": true, "options": {}}, "id": "ai-technical-analyst", "name": "AI Elite Technical Analyst", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [464, 112], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// AI Technical Analyst çıktısını database için hazırla\nconst aiAnalysis = $input.first().json;\nconst technicalData = $('Calculate Technical Indicators').first().json;\n\n// Güvenli string escape fonksiyonu\nfunction escapeString(str) {\n  if (typeof str !== 'string') return str;\n  return str.replace(/'/g, \"''\");\n}\n\n// Database için veri hazırla\nconst dbData = {\n  symbol: technicalData.symbol || 'UNKNOWN',\n  trend_direction: aiAnalysis.trend_direction || 'SIDEWAYS',\n  market_phase: aiAnalysis.market_phase || 'CONSOLIDATION',\n  buy_sell_signal: aiAnalysis.buy_sell_signal || 'BEKLE',\n  confidence_score: aiAnalysis.confidence_score || 0.5,\n  risk_level: aiAnalysis.risk_level || 'MEDIUM',\n  rsi_value: technicalData.rsi || 50,\n  macd_interpretation: aiAnalysis.macd_interpretation || 'NEUTRAL',\n  support_level: aiAnalysis.support_level || technicalData.support_level || 0,\n  resistance_level: aiAnalysis.resistance_level || technicalData.resistance_level || 0,\n  next_price_target: aiAnalysis.next_price_target || technicalData.current_price || 0,\n  stop_loss_suggestion: aiAnalysis.stop_loss_suggestion || technicalData.current_price * 0.95 || 0,\n  analysis_data_escaped: escapeString(JSON.stringify(aiAnalysis)),\n  data_quality_score: aiAnalysis.confidence_score || 0.5\n};\n\nreturn { json: dbData };"}, "id": "prepare-analysis-data", "name": "Prepare Analysis Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, 112], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO technical_analysis (symbol, trend_direction, market_phase, buy_sell_signal, confidence_score, risk_level, rsi_value, macd_signal, support_level, resistance_level, next_price_target, stop_loss_suggestion, analysis_data, timestamp, data_quality_score) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, CURRENT_TIMESTAMP, $14)", "options": {}}, "id": "save-technical-analysis", "name": "Save Enhanced Technical Analysis", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [960, 144], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "high-confidence", "leftValue": "={{ $('Prepare Analysis Data').first().json.confidence_score }}", "rightValue": 0.8, "operator": {"type": "number", "operation": "gte"}}, {"id": "clear-signal", "leftValue": "={{ $('Prepare Analysis Data').first().json.buy_sell_signal }}", "rightValue": "BEKLE", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "id": "check-strong-signals", "name": "Check Strong Signals", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [832, 384], "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/master-strategist", "sendBody": true, "bodyParameters": {"parameters": [{"name": "alert_type", "value": "strong_technical_signal"}, {"name": "source_agent", "value": "advanced_technical_analyst"}, {"name": "technical_data", "value": "={{ JSON.stringify($('Prepare Analysis Data').first().json) }}"}, {"name": "raw_indicators", "value": "={{ JSON.stringify($('Calculate Technical Indicators').first().json) }}"}, {"name": "urgency", "value": "HIGH"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "trigger-strategist", "name": "Trigger Master Strategist", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1104, 352], "onError": "continueRegularOutput"}], "pinData": {}, "connections": {"Every 5 Minutes": {"main": [[{"node": "Get Latest Market Data", "type": "main", "index": 0}]]}, "Get Latest Market Data": {"main": [[{"node": "Calculate Technical Indicators", "type": "main", "index": 0}]]}, "Calculate Technical Indicators": {"main": [[{"node": "AI Elite Technical Analyst", "type": "main", "index": 0}]]}, "AI Elite Technical Analyst": {"main": [[{"node": "Prepare Analysis Data", "type": "main", "index": 0}]]}, "Prepare Analysis Data": {"main": [[{"node": "Save Enhanced Technical Analysis", "type": "main", "index": 0}, {"node": "Check Strong Signals", "type": "main", "index": 0}]]}, "Check Strong Signals": {"main": [[{"node": "Trigger Master Strategist", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": true, "saveExecutionProgress": true}, "versionId": "325f0bbb-2592-4692-a7e0-4f8946640c27", "meta": {"instanceId": "a714df1aa11da563210a0570d83019f8d8c6597fc9f1fe509b3525f7215ea45e"}, "id": "qyMU88sLO5MNpO3B", "tags": []}