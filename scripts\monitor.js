#!/usr/bin/env node

/**
 * Production Monitoring Script
 * Author: inkbytefo
 * Description: Real-time monitoring for Multi-Agent AI Trading System
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Color functions for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function checkContainerHealth() {
  log('🏥 Container health durumu kontrol ediliyor...', 'blue');
  
  try {
    const result = execSync('docker compose -f docker-compose.prod.yml ps --format json', {
      encoding: 'utf8'
    });
    
    const containers = result.trim().split('\n').map(line => JSON.parse(line));
    
    containers.forEach(container => {
      const status = container.State;
      const health = container.Health || 'unknown';
      const name = container.Service;
      
      if (status === 'running' && (health === 'healthy' || health === 'unknown')) {
        log(`✅ ${name}: ${status} (${health})`, 'green');
      } else {
        log(`❌ ${name}: ${status} (${health})`, 'red');
      }
    });
    
    return containers.every(c => c.State === 'running');
    
  } catch (error) {
    log('❌ Container health check hatası', 'red');
    return false;
  }
}

function checkResourceUsage() {
  log('\n📊 Resource kullanımı kontrol ediliyor...', 'blue');
  
  try {
    const result = execSync('docker stats --no-stream --format "{{.Container}},{{.CPUPerc}},{{.MemUsage}},{{.MemPerc}}"', {
      encoding: 'utf8'
    });
    
    const lines = result.trim().split('\n');
    
    lines.forEach(line => {
      const [container, cpu, memory, memPerc] = line.split(',');
      const cpuNum = parseFloat(cpu.replace('%', ''));
      const memPercNum = parseFloat(memPerc.replace('%', ''));
      
      let color = 'green';
      if (cpuNum > 80 || memPercNum > 80) {
        color = 'red';
      } else if (cpuNum > 60 || memPercNum > 60) {
        color = 'yellow';
      }
      
      log(`${container}: CPU ${cpu}, Memory ${memory} (${memPerc})`, color);
    });
    
  } catch (error) {
    log('❌ Resource usage check hatası', 'red');
  }
}

function checkDiskSpace() {
  log('\n💾 Disk alanı kontrol ediliyor...', 'blue');
  
  try {
    const result = execSync('df -h /', { encoding: 'utf8' });
    const lines = result.trim().split('\n');
    const diskInfo = lines[1].split(/\s+/);
    const usagePercent = parseInt(diskInfo[4].replace('%', ''));
    
    let color = 'green';
    if (usagePercent > 90) {
      color = 'red';
    } else if (usagePercent > 80) {
      color = 'yellow';
    }
    
    log(`Disk kullanımı: ${diskInfo[4]} (${diskInfo[2]} / ${diskInfo[1]})`, color);
    
  } catch (error) {
    log('❌ Disk space check hatası', 'red');
  }
}

function checkApplicationLogs() {
  log('\n📝 Application logları kontrol ediliyor...', 'blue');
  
  try {
    // Son 50 satır log al
    const result = execSync('docker compose -f docker-compose.prod.yml logs --tail=50 trading-app', {
      encoding: 'utf8'
    });
    
    const errorLines = result.split('\n').filter(line => 
      line.toLowerCase().includes('error') || 
      line.toLowerCase().includes('exception') ||
      line.toLowerCase().includes('failed')
    );
    
    if (errorLines.length > 0) {
      log(`⚠️  ${errorLines.length} error bulundu son 50 satırda`, 'yellow');
      errorLines.slice(-5).forEach(line => {
        log(`  ${line}`, 'red');
      });
    } else {
      log('✅ Son loglar temiz', 'green');
    }
    
  } catch (error) {
    log('❌ Log check hatası', 'red');
  }
}

function checkDatabaseConnections() {
  log('\n🗄️  Veritabanı bağlantıları kontrol ediliyor...', 'blue');
  
  try {
    const result = execSync('docker compose -f docker-compose.prod.yml exec -T postgres psql -U n8n_user -d n8n_trading -c "SELECT count(*) FROM pg_stat_activity WHERE state = \'active\';"', {
      encoding: 'utf8'
    });
    
    const activeConnections = parseInt(result.split('\n')[2].trim());
    
    let color = 'green';
    if (activeConnections > 50) {
      color = 'red';
    } else if (activeConnections > 30) {
      color = 'yellow';
    }
    
    log(`Aktif veritabanı bağlantıları: ${activeConnections}`, color);
    
  } catch (error) {
    log('❌ Database connection check hatası', 'red');
  }
}

function checkN8nWorkflows() {
  log('\n🔄 n8n workflow durumu kontrol ediliyor...', 'blue');
  
  try {
    // n8n API'den workflow durumlarını al
    const result = execSync('curl -s http://localhost:5678/api/v1/workflows', {
      encoding: 'utf8'
    });
    
    const workflows = JSON.parse(result);
    const activeWorkflows = workflows.data.filter(w => w.active);
    
    log(`Toplam workflow: ${workflows.data.length}`, 'cyan');
    log(`Aktif workflow: ${activeWorkflows.length}`, activeWorkflows.length > 0 ? 'green' : 'yellow');
    
    if (activeWorkflows.length === 0) {
      log('⚠️  Hiç aktif workflow yok!', 'yellow');
    }
    
  } catch (error) {
    log('❌ n8n workflow check hatası', 'red');
  }
}

function generateReport() {
  const timestamp = new Date().toISOString();
  const reportPath = path.join('logs', `health-report-${timestamp.split('T')[0]}.log`);
  
  try {
    // Ensure logs directory exists
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs', { recursive: true });
    }
    
    const report = {
      timestamp,
      containers: execSync('docker compose -f docker-compose.prod.yml ps --format json', { encoding: 'utf8' }),
      resources: execSync('docker stats --no-stream --format json', { encoding: 'utf8' }),
      disk: execSync('df -h /', { encoding: 'utf8' }),
      memory: execSync('free -h', { encoding: 'utf8' }).catch(() => 'N/A'),
      uptime: execSync('uptime', { encoding: 'utf8' }).catch(() => 'N/A')
    };
    
    fs.appendFileSync(reportPath, JSON.stringify(report, null, 2) + '\n');
    log(`📊 Rapor kaydedildi: ${reportPath}`, 'cyan');
    
  } catch (error) {
    log('❌ Rapor oluşturma hatası', 'red');
  }
}

function sendAlert(message, level = 'warning') {
  // Telegram bot ile alert gönder (eğer yapılandırılmışsa)
  try {
    const telegramToken = process.env.TELEGRAM_BOT_TOKEN;
    const chatId = process.env.TELEGRAM_CHAT_ID;
    
    if (telegramToken && chatId) {
      const alertMessage = `🚨 Trading System Alert\n\nLevel: ${level.toUpperCase()}\nMessage: ${message}\nTime: ${new Date().toISOString()}`;
      
      execSync(`curl -s -X POST "https://api.telegram.org/bot${telegramToken}/sendMessage" -d "chat_id=${chatId}&text=${encodeURIComponent(alertMessage)}"`, {
        stdio: 'pipe'
      });
      
      log('📱 Telegram alert gönderildi', 'cyan');
    }
  } catch (error) {
    log('❌ Alert gönderme hatası', 'red');
  }
}

function runFullHealthCheck() {
  log('🔍 Tam sistem health check başlatılıyor...', 'magenta');
  log('==========================================\n', 'magenta');
  
  const issues = [];
  
  // Container health
  if (!checkContainerHealth()) {
    issues.push('Container health problems detected');
  }
  
  // Resource usage
  checkResourceUsage();
  
  // Disk space
  checkDiskSpace();
  
  // Application logs
  checkApplicationLogs();
  
  // Database connections
  checkDatabaseConnections();
  
  // n8n workflows
  checkN8nWorkflows();
  
  // Generate report
  generateReport();
  
  // Send alerts if issues found
  if (issues.length > 0) {
    const alertMessage = `Health check issues: ${issues.join(', ')}`;
    sendAlert(alertMessage, 'critical');
    log(`\n🚨 ${issues.length} sorun tespit edildi!`, 'red');
  } else {
    log('\n✅ Tüm health check\'ler başarılı', 'green');
  }
  
  log('\n📊 Monitoring tamamlandı', 'cyan');
}

function startContinuousMonitoring() {
  log('🔄 Sürekli monitoring başlatılıyor...', 'blue');
  log('Çıkmak için Ctrl+C basın\n', 'yellow');
  
  const interval = process.env.MONITOR_INTERVAL || 300000; // 5 dakika default
  
  // İlk check
  runFullHealthCheck();
  
  // Sürekli monitoring
  setInterval(() => {
    console.clear();
    runFullHealthCheck();
  }, interval);
}

// Ana monitoring fonksiyonu
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--continuous')) {
    startContinuousMonitoring();
  } else {
    runFullHealthCheck();
  }
}

// Script'i çalıştır
if (require.main === module) {
  main();
}
