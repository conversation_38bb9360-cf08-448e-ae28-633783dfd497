{"name": "multi-agent-ai-trading-system", "version": "1.0.0", "description": "Multi-Agent AI Trading System built with n8n workflow automation", "main": "index.js", "scripts": {"start": "node scripts/start.js", "setup": "node scripts/setup.js", "security:setup": "node scripts/security-setup.js", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "test": "jest", "test:security": "jest --testPathPattern=security", "lint": "eslint .", "format": "prettier --write .", "docker:dev": "docker compose up -d", "docker:dev:build": "docker compose up -d --build", "docker:dev:down": "docker compose down", "docker:dev:logs": "docker compose logs -f", "docker:prod": "docker compose -f docker-compose.prod.yml up -d", "docker:prod:build": "docker compose -f docker-compose.prod.yml up -d --build", "docker:prod:down": "docker compose -f docker-compose.prod.yml down", "docker:deploy": "node scripts/deploy.js", "docker:monitor": "node scripts/monitor.js", "docker:monitor:continuous": "node scripts/monitor.js --continuous", "performance:test": "node scripts/performance-test.js", "db:optimize": "node scripts/db-optimize.js"}, "keywords": ["n8n", "ai", "trading", "crypto", "automation", "multi-agent", "workflow", "postgresql", "telegram"], "author": "inkbytefo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/inkbytefo/multi-agent-ai-trading-system.git"}, "bugs": {"url": "https://github.com/inkbytefo/multi-agent-ai-trading-system/issues"}, "homepage": "https://github.com/inkbytefo/multi-agent-ai-trading-system#readme", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "node-telegram-bot-api": "^0.64.0", "winston": "^3.11.0", "cron": "^3.1.6", "technical-indicators": "^3.1.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "redis": "^4.6.10", "bull": "^4.12.2", "ws": "^8.14.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.54.0", "prettier": "^3.1.0", "@types/node": "^20.9.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "n8n": {"version": ">=1.0.0", "workflows": ["workflows/data-harvester-agent.json", "workflows/technical-analyst-agent.json", "workflows/sentiment-analyst-agent.json", "workflows/risk-manager-agent.json", "workflows/master-strategist-brain.json", "workflows/telegram-herald-assistant.json"]}, "config": {"database": {"host": "localhost", "port": 5432, "database": "n8n_trading", "user": "n8n_user"}, "n8n": {"host": "localhost", "port": 5678}}}