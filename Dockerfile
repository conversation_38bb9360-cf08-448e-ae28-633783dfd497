# Multi-Agent AI Trading System - Production Dockerfile
# Author: inkbytefo
# Description: Production-ready Docker image with multi-stage build and security best practices

# ================================
# Stage 1: Build Dependencies
# ================================
FROM node:18-alpine AS dependencies

# Install build dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    postgresql-client \
    curl

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including devDependencies for build)
RUN npm ci --only=production --silent && \
    npm cache clean --force

# ================================
# Stage 2: Build Application
# ================================
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies including dev dependencies
RUN npm ci --silent

# Copy source code
COPY . .

# Run tests to ensure build quality
RUN npm test

# Run linting and formatting checks
RUN npm run lint

# Clean up dev dependencies
RUN npm prune --production

# ================================
# Stage 3: Production Runtime
# ================================
FROM node:18-alpine AS production

# Install runtime dependencies
RUN apk add --no-cache \
    postgresql-client \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Create app directory with proper permissions
WORKDIR /app
RUN chown -R nodejs:nodejs /app

# Copy production dependencies from dependencies stage
COPY --from=dependencies --chown=nodejs:nodejs /app/node_modules ./node_modules

# Copy application code from builder stage
COPY --from=builder --chown=nodejs:nodejs /app/package*.json ./
COPY --from=builder --chown=nodejs:nodejs /app/scripts ./scripts
COPY --from=builder --chown=nodejs:nodejs /app/database ./database
COPY --from=builder --chown=nodejs:nodejs /app/workflows ./workflows
COPY --from=builder --chown=nodejs:nodejs /app/.env.example ./

# Create necessary directories with proper permissions
RUN mkdir -p logs backups temp && \
    chown -R nodejs:nodejs logs backups temp

# Switch to non-root user
USER nodejs

# Expose application port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Default command
CMD ["node", "scripts/start.js"]

# ================================
# Metadata
# ================================
LABEL maintainer="inkbytefo <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="Multi-Agent AI Trading System"
LABEL org.opencontainers.image.source="https://github.com/inkbytefo/multi-agent-ai-trading-system"
LABEL org.opencontainers.image.documentation="https://github.com/inkbytefo/multi-agent-ai-trading-system#readme"
LABEL org.opencontainers.image.licenses="MIT"
