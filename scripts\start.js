#!/usr/bin/env node

/**
 * Multi-Agent AI Trading System Starter Script
 * Author: inkbytefo
 * 
 * Bu script sistemi başlatır ve durumunu kontrol eder.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Renk kodları
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkSystemHealth() {
  log('🏥 Sistem sağlığı kontrol ediliyor...', 'blue');
  
  // .env dosyası kontrolü
  if (!fs.existsSync('.env')) {
    log('❌ .env dosyası bulunamadı. Önce setup.js çalıştırın.', 'red');
    process.exit(1);
  }
  
  // Gerekli environment variables
  const requiredEnvVars = [
    'DB_POSTGRESDB_HOST',
    'DB_POSTGRESDB_DATABASE',
    'DB_POSTGRESDB_USER',
    'OPENAI_API_KEY',
    'TELEGRAM_BOT_TOKEN'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  if (missingVars.length > 0) {
    log(`❌ Eksik environment variables: ${missingVars.join(', ')}`, 'red');
    log('🔧 .env dosyasını kontrol edin', 'yellow');
    process.exit(1);
  }
  
  log('✅ Environment variables tamam', 'green');
}

function checkDatabase() {
  log('🗄️  Veritabanı bağlantısı kontrol ediliyor...', 'blue');
  
  const dbConfig = {
    host: process.env.DB_POSTGRESDB_HOST,
    port: process.env.DB_POSTGRESDB_PORT || 5432,
    database: process.env.DB_POSTGRESDB_DATABASE,
    user: process.env.DB_POSTGRESDB_USER,
    password: process.env.DB_POSTGRESDB_PASSWORD
  };
  
  try {
    const testCommand = `PGPASSWORD=${dbConfig.password} psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.user} -d ${dbConfig.database} -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"`;
    const result = execSync(testCommand, { encoding: 'utf8', stdio: 'pipe' });
    
    const tableCount = parseInt(result.split('\n')[2].trim());
    if (tableCount >= 8) {
      log('✅ Veritabanı bağlantısı ve şema tamam', 'green');
    } else {
      log('⚠️  Veritabanı şeması eksik. database/schema.sql çalıştırın', 'yellow');
    }
  } catch (error) {
    log('❌ Veritabanı bağlantısı başarısız', 'red');
    log('🔧 Veritabanı ayarlarını kontrol edin', 'yellow');
    process.exit(1);
  }
}

function checkN8nStatus() {
  log('🔄 n8n durumu kontrol ediliyor...', 'blue');
  
  const n8nHost = process.env.N8N_HOST || 'localhost';
  const n8nPort = process.env.N8N_PORT || 5678;
  
  try {
    const curlCommand = `curl -s -o /dev/null -w "%{http_code}" http://${n8nHost}:${n8nPort}/healthz`;
    const statusCode = execSync(curlCommand, { encoding: 'utf8', stdio: 'pipe' }).trim();
    
    if (statusCode === '200') {
      log('✅ n8n çalışıyor', 'green');
      return true;
    } else {
      log('❌ n8n yanıt vermiyor', 'red');
      return false;
    }
  } catch (error) {
    log('❌ n8n bağlantısı başarısız', 'red');
    return false;
  }
}

function startN8n() {
  log('🚀 n8n başlatılıyor...', 'blue');
  
  const n8nCommand = [
    'docker', 'run', '-d',
    '--name', 'n8n-trading',
    '-p', '5678:5678',
    '-e', `DB_TYPE=postgresdb`,
    '-e', `DB_POSTGRESDB_HOST=${process.env.DB_POSTGRESDB_HOST}`,
    '-e', `DB_POSTGRESDB_PORT=${process.env.DB_POSTGRESDB_PORT || 5432}`,
    '-e', `DB_POSTGRESDB_DATABASE=${process.env.DB_POSTGRESDB_DATABASE}`,
    '-e', `DB_POSTGRESDB_USER=${process.env.DB_POSTGRESDB_USER}`,
    '-e', `DB_POSTGRESDB_PASSWORD=${process.env.DB_POSTGRESDB_PASSWORD}`,
    '-e', `N8N_ENCRYPTION_KEY=${process.env.N8N_ENCRYPTION_KEY || 'mysecretkey'}`,
    '-v', 'n8n_data:/home/<USER>/.n8n',
    'n8nio/n8n'
  ];
  
  try {
    // Mevcut container'ı durdur
    try {
      execSync('docker stop n8n-trading', { stdio: 'pipe' });
      execSync('docker rm n8n-trading', { stdio: 'pipe' });
    } catch (error) {
      // Container yoksa sorun değil
    }
    
    // Yeni container başlat
    execSync(n8nCommand.join(' '), { stdio: 'pipe' });
    log('✅ n8n Docker container başlatıldı', 'green');
    
    // Başlatma için bekle
    log('⏳ n8n başlatılması bekleniyor...', 'yellow');
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      if (checkN8nStatus()) {
        log('✅ n8n hazır!', 'green');
        return true;
      }
      attempts++;
      execSync('sleep 2');
    }
    
    log('❌ n8n başlatma timeout', 'red');
    return false;
    
  } catch (error) {
    log(`❌ n8n başlatma hatası: ${error.message}`, 'red');
    return false;
  }
}

function displaySystemStatus() {
  log('\n📊 Sistem Durumu', 'cyan');
  log('================', 'cyan');
  
  const n8nUrl = `http://${process.env.N8N_HOST || 'localhost'}:${process.env.N8N_PORT || 5678}`;
  
  log(`🌐 n8n Arayüzü: ${n8nUrl}`, 'green');
  log(`🗄️  Veritabanı: ${process.env.DB_POSTGRESDB_HOST}:${process.env.DB_POSTGRESDB_PORT}/${process.env.DB_POSTGRESDB_DATABASE}`, 'green');
  log(`🤖 Telegram Bot: ${process.env.TELEGRAM_BOT_TOKEN ? 'Yapılandırılmış' : 'Yapılandırılmamış'}`, process.env.TELEGRAM_BOT_TOKEN ? 'green' : 'yellow');
  log(`🧠 OpenAI API: ${process.env.OPENAI_API_KEY ? 'Yapılandırılmış' : 'Yapılandırılmamış'}`, process.env.OPENAI_API_KEY ? 'green' : 'yellow');
  
  log('\n📋 Sonraki Adımlar:', 'cyan');
  log('1. n8n arayüzüne gidin ve credentials\'ları yapılandırın', 'yellow');
  log('2. workflows/ klasöründeki JSON dosyalarını import edin', 'yellow');
  log('3. Workflow\'ları sırayla aktif hale getirin', 'yellow');
  log('4. Telegram bot\'unuzu test edin', 'yellow');
  
  log('\n🎯 Workflow Sırası:', 'cyan');
  log('1. 🕵️  Data Harvester Agent', 'yellow');
  log('2. 📈 Technical Analyst Agent', 'yellow');
  log('3. 📰 Sentiment Analyst Agent', 'yellow');
  log('4. 🛡️  Risk Manager Agent', 'yellow');
  log('5. 🧠 Master Strategist Brain', 'yellow');
  log('6. 💬 Telegram Herald Assistant', 'yellow');
}

function monitorSystem() {
  log('\n👁️  Sistem izleme başlatılıyor...', 'blue');
  log('Çıkmak için Ctrl+C basın\n', 'yellow');
  
  setInterval(() => {
    const timestamp = new Date().toISOString();
    const isN8nHealthy = checkN8nStatus();
    
    if (isN8nHealthy) {
      log(`[${timestamp}] ✅ Sistem sağlıklı`, 'green');
    } else {
      log(`[${timestamp}] ❌ n8n yanıt vermiyor`, 'red');
    }
  }, 30000); // Her 30 saniyede kontrol et
}

// Ana başlatma fonksiyonu
async function main() {
  log('🚀 Multi-Agent AI Trading System', 'magenta');
  log('==================================\n', 'magenta');
  
  try {
    checkSystemHealth();
    checkDatabase();
    
    if (!checkN8nStatus()) {
      log('🔄 n8n çalışmıyor, başlatılıyor...', 'yellow');
      if (!startN8n()) {
        log('❌ n8n başlatılamadı', 'red');
        process.exit(1);
      }
    }
    
    displaySystemStatus();
    
    // Monitoring modunu başlat
    if (process.argv.includes('--monitor')) {
      monitorSystem();
    }
    
  } catch (error) {
    log(`\n❌ Başlatma hatası: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Script'i çalıştır
if (require.main === module) {
  main();
}
