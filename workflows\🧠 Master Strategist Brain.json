{"name": "🧠 Master Strategist Brain", "nodes": [{"parameters": {"httpMethod": "POST", "path": "master-strategist", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Agent <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [0, 160], "webhookId": "bc96f34f-01af-427f-ae86-01d10f186639", "onError": "continueRegularOutput"}, {"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "id": "schedule-trigger", "name": "Every 15 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [32, 416], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT 'market_data' as source, ai_quality_score as score, ai_analysis as data, timestamp FROM market_data WHERE timestamp > NOW() - INTERVAL '2 hours' UNION ALL SELECT 'technical_analysis' as source, confidence_score as score, analysis_data as data, timestamp FROM technical_analysis WHERE timestamp > NOW() - INTERVAL '2 hours' UNION ALL SELECT 'sentiment_analysis' as source, confidence_level as score, analysis_data as data, timestamp FROM sentiment_analysis WHERE timestamp > NOW() - INTERVAL '2 hours' UNION ALL SELECT 'risk_assessment' as source, confidence_level as score, assessment_data as data, timestamp FROM risk_assessments WHERE timestamp > NOW() - INTERVAL '2 hours' ORDER BY timestamp DESC LIMIT 100", "options": {}}, "id": "gather-all-data", "name": "<PERSON>ather All Agent Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [256, 304], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "Sen bir kripto para master stratejistisin. <PERSON>ü<PERSON> uzman ajanlarından gelen verileri sentezle:\n\n{{JSON.stringify($json, null, 2)}}\n\nGörevlerin:\n1. Tüm veri kaynaklarını birleştir\n2. Çelişkileri tespit et ve çöz\n3. Gizli pattern'leri bul\n4. Kapsamlı piyasa senaryosu oluştur\n5. Net trading stratejisi belirle\n6. <PERSON><PERSON>ven skorunu hesapla\n7. Alternatif senaryolar sun\n\nJSON formatında master strateji raporu sun.", "hasOutputParser": true, "options": {}}, "id": "ai-master-strategist", "name": "AI Master Strategist Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [304, 64], "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "Sen bir strateji doğru<PERSON>. Master strategist'ın önerilerini analiz et:\n\n{{JSON.stringify($json, null, 2)}}\n\nGörevlerin:\n1. Strateji tutarlılığını kontrol et\n2. Risk seviyelerini değerlendir\n3. Gerçekçilik testini yap\n4. İyileştirme önerileri sun\n5. Final güven skorunu belirle\n6. Onay/red kararı ver\n\nJSON formatında doğrulama raporu sun.", "hasOutputParser": true, "options": {}}, "id": "ai-strategy-validator", "name": "AI Strategy Validator Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [448, 272], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// AI Strategy Validator çıktısını database için hazırla\nconst validatorOutput = $input.first().json;\n\n// Güvenli string escape fonksiyonu\nfunction escapeString(str) {\n  if (typeof str !== 'string') return str;\n  return str.replace(/'/g, \"''\");\n}\n\n// Database için veri hazırla\nconst dbData = {\n  strategy_id: validatorOutput.validated_strategy?.strategy_id || 'STRATEGY_' + Date.now(),\n  market_scenario: validatorOutput.validated_strategy?.market_scenario || 'UNKNOWN',\n  confidence_score: validatorOutput.final_confidence || 0.5,\n  primary_recommendation_escaped: escapeString(JSON.stringify(validatorOutput.validated_strategy?.primary_recommendation || {})),\n  validation_score: validatorOutput.validation_score || 0.5,\n  strategy_approved: validatorOutput.strategy_approved || false,\n  raw_analysis_data_escaped: escapeString(JSON.stringify(validatorOutput))\n};\n\nreturn { json: dbData };"}, "id": "prepare-synthesis-data", "name": "Prepare Synthesis Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [672, 112], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO synthesis_reports (strategy_id, market_scenario, confidence_score, primary_recommendation, validation_score, strategy_approved, raw_analysis_data, timestamp) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)", "options": {}}, "id": "save-synthesis-report", "name": "Save Synthesis Report", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [896, 112], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "high-confidence", "leftValue": "={{ $('Prepare Synthesis Data').first().json.confidence_score }}", "rightValue": 0.75, "operator": {"type": "number", "operation": "gte"}}, {"id": "strategy-approved", "leftValue": "={{ $('Prepare Synthesis Data').first().json.strategy_approved }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-high-confidence", "name": "Check High Confidence", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [800, 400], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Actionable Insight için veri hazırla\nconst validatorOutput = $('AI Strategy Validator Agent').first().json;\nconst synthesisData = $('Prepare Synthesis Data').first().json;\n\n// Güvenli string escape fonksiyonu\nfunction escapeString(str) {\n  if (typeof str !== 'string') return str;\n  return str.replace(/'/g, \"''\");\n}\n\n// Actionable insight veri hazırla\nconst recommendation = validatorOutput.validated_strategy?.primary_recommendation || {};\nconst dbData = {\n  strategy_id: synthesisData.strategy_id,\n  action_type: recommendation.action || 'HOLD',\n  symbols_escaped: escapeString(JSON.stringify(recommendation.symbols || [])),\n  target_price: recommendation.target_price || 0,\n  stop_loss: recommendation.stop_loss || 0,\n  position_size: recommendation.position_size || 0,\n  confidence_level: synthesisData.confidence_score,\n  reasoning_escaped: escapeString(validatorOutput.validated_strategy?.reasoning_tr || 'No reasoning provided'),\n  execution_timeline: validatorOutput.validated_strategy?.execution_timeline || 'IMMEDIATE'\n};\n\nreturn { json: dbData };"}, "id": "prepare-insight-data", "name": "Prepare Insight Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1008, 272], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO actionable_insights (strategy_id, action_type, symbols, target_price, stop_loss, position_size, confidence_level, reasoning, execution_timeline, timestamp) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)", "options": {}}, "id": "save-actionable-insight", "name": "Save Actionable Insight", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1232, 272], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/telegram-herald", "sendBody": true, "bodyParameters": {"parameters": [{"name": "alert_type", "value": "high_confidence_strategy"}, {"name": "source_agent", "value": "master_strategist"}, {"name": "strategy_data", "value": "={{ JSON.stringify($('AI Strategy Validator Agent').first().json) }}"}, {"name": "urgency", "value": "HIGH"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "trigger-telegram-herald", "name": "Trigger Telegram Herald", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1056, 512], "onError": "continueRegularOutput"}, {"parameters": {"options": {}}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.5, "position": [1328, 240], "onError": "continueRegularOutput"}], "pinData": {}, "connections": {"Agent Alerts Webhook": {"main": [[{"node": "<PERSON>ather All Agent Data", "type": "main", "index": 0}]]}, "Every 15 Minutes": {"main": [[{"node": "<PERSON>ather All Agent Data", "type": "main", "index": 0}]]}, "Gather All Agent Data": {"main": [[{"node": "AI Master Strategist Agent", "type": "main", "index": 0}]]}, "AI Master Strategist Agent": {"main": [[{"node": "AI Strategy Validator Agent", "type": "main", "index": 0}]]}, "AI Strategy Validator Agent": {"main": [[{"node": "Prepare Synthesis Data", "type": "main", "index": 0}]]}, "Prepare Synthesis Data": {"main": [[{"node": "Save Synthesis Report", "type": "main", "index": 0}, {"node": "Check High Confidence", "type": "main", "index": 0}]]}, "Check High Confidence": {"main": [[{"node": "Prepare Insight Data", "type": "main", "index": 0}, {"node": "Trigger Telegram Herald", "type": "main", "index": 0}]]}, "Prepare Insight Data": {"main": [[{"node": "Save Actionable Insight", "type": "main", "index": 0}]]}, "Save Synthesis Report": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": true, "saveExecutionProgress": true}, "versionId": "5918eba3-223a-4988-aa3f-d6b89742ef39", "meta": {"instanceId": "a714df1aa11da563210a0570d83019f8d8c6597fc9f1fe509b3525f7215ea45e"}, "id": "fQwLNucFc4SzweF7", "tags": []}