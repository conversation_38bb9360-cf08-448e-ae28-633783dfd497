-- TimescaleDB Initialization for Trading System
-- Author: inkbytefo
-- Description: Convert regular tables to hypertables for time-series optimization

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Convert market_data to hypertable
SELECT create_hypertable('market_data', 'timestamp', 
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Convert technical_analysis to hypertable  
SELECT create_hypertable('technical_analysis', 'timestamp',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Convert sentiment_analysis to hypertable
SELECT create_hypertable('sentiment_analysis', 'timestamp',
    chunk_time_interval => INTERVAL '2 hours',
    if_not_exists => TRUE
);

-- Convert risk_assessments to hypertable
SELECT create_hypertable('risk_assessments', 'timestamp',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Convert synthesis_reports to hypertable
SELECT create_hypertable('synthesis_reports', 'timestamp',
    chunk_time_interval => INTERVAL '4 hours',
    if_not_exists => TRUE
);

-- Create continuous aggregates for real-time analytics
CREATE MATERIALIZED VIEW market_data_1min
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 minute', timestamp) AS bucket,
    symbol,
    AVG(price) as avg_price,
    MAX(price) as max_price,
    MIN(price) as min_price,
    SUM(volume_24h) as total_volume,
    AVG(ai_quality_score) as avg_quality,
    COUNT(*) as data_points
FROM market_data
GROUP BY bucket, symbol;

-- Refresh policy for 1-minute aggregates
SELECT add_continuous_aggregate_policy('market_data_1min',
    start_offset => INTERVAL '1 hour',
    end_offset => INTERVAL '1 minute',
    schedule_interval => INTERVAL '1 minute'
);

-- 5-minute aggregates for technical analysis
CREATE MATERIALIZED VIEW market_data_5min
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('5 minutes', timestamp) AS bucket,
    symbol,
    first(price, timestamp) as open_price,
    max(price) as high_price,
    min(price) as low_price,
    last(price, timestamp) as close_price,
    sum(volume_24h) as volume,
    avg(ai_quality_score) as avg_quality
FROM market_data
GROUP BY bucket, symbol;

-- Refresh policy for 5-minute aggregates
SELECT add_continuous_aggregate_policy('market_data_5min',
    start_offset => INTERVAL '2 hours',
    end_offset => INTERVAL '5 minutes',
    schedule_interval => INTERVAL '5 minutes'
);

-- Hourly aggregates for long-term analysis
CREATE MATERIALIZED VIEW market_data_1hour
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', timestamp) AS bucket,
    symbol,
    first(price, timestamp) as open_price,
    max(price) as high_price,
    min(price) as low_price,
    last(price, timestamp) as close_price,
    sum(volume_24h) as volume,
    avg(price_change_percentage_24h) as avg_change_pct,
    avg(ai_quality_score) as avg_quality,
    count(*) as data_points
FROM market_data
GROUP BY bucket, symbol;

-- Refresh policy for hourly aggregates
SELECT add_continuous_aggregate_policy('market_data_1hour',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour'
);

-- Data retention policies
SELECT add_retention_policy('market_data', INTERVAL '30 days');
SELECT add_retention_policy('technical_analysis', INTERVAL '30 days');
SELECT add_retention_policy('sentiment_analysis', INTERVAL '30 days');
SELECT add_retention_policy('risk_assessments', INTERVAL '30 days');
SELECT add_retention_policy('synthesis_reports', INTERVAL '90 days');

-- Compression policies for older data
SELECT add_compression_policy('market_data', INTERVAL '7 days');
SELECT add_compression_policy('technical_analysis', INTERVAL '7 days');
SELECT add_compression_policy('sentiment_analysis', INTERVAL '7 days');
SELECT add_compression_policy('risk_assessments', INTERVAL '7 days');

-- Create indexes for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_symbol_bucket 
ON market_data_1min (symbol, bucket DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_5min_symbol_bucket 
ON market_data_5min (symbol, bucket DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_1hour_symbol_bucket 
ON market_data_1hour (symbol, bucket DESC);

-- Grant permissions
GRANT SELECT ON market_data_1min TO n8n_user;
GRANT SELECT ON market_data_5min TO n8n_user;
GRANT SELECT ON market_data_1hour TO n8n_user;

-- Log successful initialization
INSERT INTO system_logs (log_level, message, component, timestamp)
VALUES ('INFO', 'TimescaleDB hypertables and continuous aggregates initialized successfully', 'database', NOW());
