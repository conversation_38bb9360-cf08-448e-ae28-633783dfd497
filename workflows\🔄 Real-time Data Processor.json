{"name": "🔄 Real-time Data Processor", "nodes": [{"parameters": {"httpMethod": "POST", "path": "realtime-data", "responseMode": "responseNode", "options": {"rawBody": true}}, "id": "webhook-realtime", "name": "Real-time Data Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [0, 160], "webhookId": "realtime-data-processor", "onError": "continueRegularOutput"}, {"parameters": {"rule": {"interval": [{"field": "seconds", "secondsInterval": 30}]}}, "id": "schedule-realtime", "name": "Every 30 Seconds", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 320], "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "data-source-check", "leftValue": "={{ $json.source }}", "rightValue": "webhook", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "data-source-router", "name": "Data Source Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [200, 240], "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "process-timestamp", "name": "processing_timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "data-quality", "name": "data_quality_score", "value": "={{ Math.random() * 0.3 + 0.7 }}", "type": "number"}, {"id": "source-priority", "name": "source_priority", "value": "={{ $json.source === 'binance' ? 1 : $json.source === 'coingecko' ? 2 : 3 }}", "type": "number"}]}, "options": {}}, "id": "data-enrichment", "name": "Data Enrichment", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [400, 160], "onError": "continueRegularOutput"}, {"parameters": {"model": "gpt-4o-mini", "options": {"temperature": 0.1, "maxTokens": 500}, "messages": {"messages": [{"role": "system", "content": "You are a financial data quality analyst. Analyze the provided market data and return a JSON object with quality_score (0-1), reliability_assessment, anomaly_flags, and confidence_level."}, {"role": "user", "content": "Analyze this market data: {{ JSON.stringify($json) }}"}]}}, "id": "ai-quality-check", "name": "AI Quality Assessment", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.4, "position": [600, 160], "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "quality-threshold", "leftValue": "={{ $json.data_quality_score }}", "rightValue": 0.6, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "quality-filter", "name": "Quality Filter", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [800, 160], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO market_data_realtime (symbol, price, volume_24h, price_change_percentage_24h, source, ai_quality_score, ai_analysis, processing_timestamp, source_priority) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) ON CONFLICT (symbol, source, processing_timestamp) DO UPDATE SET price = EXCLUDED.price, volume_24h = EXCLUDED.volume_24h, ai_quality_score = EXCLUDED.ai_quality_score", "parameters": {"parameters": [{"name": "symbol", "value": "={{ $json.symbol }}"}, {"name": "price", "value": "={{ $json.price }}"}, {"name": "volume_24h", "value": "={{ $json.volume_24h }}"}, {"name": "price_change_percentage_24h", "value": "={{ $json.price_change_percentage_24h }}"}, {"name": "source", "value": "={{ $json.source }}"}, {"name": "ai_quality_score", "value": "={{ $json.data_quality_score }}"}, {"name": "ai_analysis", "value": "={{ JSON.stringify($json.ai_analysis || {}) }}"}, {"name": "processing_timestamp", "value": "={{ $json.processing_timestamp }}"}, {"name": "source_priority", "value": "={{ $json.source_priority }}"}]}, "options": {}}, "id": "store-realtime-data", "name": "Store Real-time Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [1000, 80], "credentials": {"postgres": {"id": "trading-postgres", "name": "Trading PostgreSQL"}}, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "significant-change", "leftValue": "={{ Math.abs($json.price_change_percentage_24h) }}", "rightValue": 5, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "significant-change-detector", "name": "Significant Change Detector", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1200, 160], "onError": "continueRegularOutput"}, {"parameters": {"url": "http://n8n-main:5678/webhook/master-strategist", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"trigger_type\": \"significant_price_change\",\n  \"symbol\": $json.symbol,\n  \"price_change\": $json.price_change_percentage_24h,\n  \"current_price\": $json.price,\n  \"quality_score\": $json.data_quality_score,\n  \"timestamp\": $json.processing_timestamp\n}) }}", "options": {"timeout": 10000}}, "id": "trigger-master-strategist", "name": "Trigger Master Strategist", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1400, 80], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO system_alerts (alert_type, severity, message, data, timestamp) VALUES ('SIGNIFICANT_PRICE_CHANGE', 'HIGH', $1, $2, NOW())", "parameters": {"parameters": [{"name": "message", "value": "={{ `Significant price change detected for ${$json.symbol}: ${$json.price_change_percentage_24h}%` }}"}, {"name": "data", "value": "={{ JSON.stringify($json) }}"}]}, "options": {}}, "id": "log-alert", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [1400, 240], "credentials": {"postgres": {"id": "trading-postgres", "name": "Trading PostgreSQL"}}, "onError": "continueRegularOutput"}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify({\n  \"status\": \"success\",\n  \"processed\": true,\n  \"quality_score\": $json.data_quality_score,\n  \"timestamp\": $json.processing_timestamp\n}) }}"}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1600, 160], "onError": "continueRegularOutput"}], "connections": {"webhook-realtime": {"main": [[{"node": "data-source-router", "type": "main", "index": 0}]]}, "schedule-realtime": {"main": [[{"node": "data-source-router", "type": "main", "index": 0}]]}, "data-source-router": {"main": [[{"node": "data-enrichment", "type": "main", "index": 0}]]}, "data-enrichment": {"main": [[{"node": "ai-quality-check", "type": "main", "index": 0}]]}, "ai-quality-check": {"main": [[{"node": "quality-filter", "type": "main", "index": 0}]]}, "quality-filter": {"main": [[{"node": "store-realtime-data", "type": "main", "index": 0}]]}, "store-realtime-data": {"main": [[{"node": "significant-change-detector", "type": "main", "index": 0}]]}, "significant-change-detector": {"main": [[{"node": "trigger-master-strategist", "type": "main", "index": 0}, {"node": "log-alert", "type": "main", "index": 0}]]}, "trigger-master-strategist": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}, "log-alert": {"main": [[{"node": "success-response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": {"id": "error-handler-workflow"}}, "staticData": {}, "tags": [{"createdAt": "2025-01-22T10:00:00.000Z", "updatedAt": "2025-01-22T10:00:00.000Z", "id": "realtime-processing", "name": "Real-time Processing"}], "triggerCount": 2, "updatedAt": "2025-01-22T10:00:00.000Z", "versionId": "1.0.0"}