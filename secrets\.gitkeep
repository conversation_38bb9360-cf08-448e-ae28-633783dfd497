# Secrets Directory

This directory contains sensitive information for production deployment.

## Files to create:

1. `openai_api_key.txt` - OpenAI API key
2. `telegram_bot_token.txt` - Telegram bot token
3. `binance_api_key.txt` - Binance API key (optional)
4. `binance_secret_key.txt` - Binance secret key (optional)

## Security Notes:

- Never commit actual secret files to version control
- Use proper file permissions (600) for secret files
- Consider using external secret management systems in production
- Rotate secrets regularly

## Example usage:

```bash
# Create secret files
echo "your_openai_api_key" > secrets/openai_api_key.txt
echo "your_telegram_bot_token" > secrets/telegram_bot_token.txt

# Set proper permissions
chmod 600 secrets/*.txt
```
