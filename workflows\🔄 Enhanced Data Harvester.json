{"name": "🔄 Enhanced Data Harvester", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 1}]}}, "id": "schedule-trigger", "name": "Every 1 Minute", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 200], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT symbol FROM trading_symbols WHERE active = true ORDER BY priority DESC", "options": {}}, "id": "get-active-symbols", "name": "Get Active Trading Symbols", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [200, 200], "credentials": {"postgres": {"id": "trading-postgres", "name": "Trading PostgreSQL"}}, "onError": "continueRegularOutput"}, {"parameters": {"batchSize": 5, "options": {}}, "id": "split-symbols", "name": "Split Symbols in Batches", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [400, 200], "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "symbols-array", "name": "symbols_array", "value": "={{ $input.all().map(item => item.json.symbol) }}", "type": "array"}, {"id": "symbols-string", "name": "symbols_string", "value": "={{ JSON.stringify($input.all().map(item => item.json.symbol + 'USDT')) }}", "type": "string"}]}, "options": {}}, "id": "prepare-api-params", "name": "Prepare API Parameters", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [600, 200], "onError": "continueRegularOutput"}, {"parameters": {"url": "https://api.binance.com/api/v3/ticker/24hr", "sendQuery": true, "queryParameters": {"parameters": [{"name": "symbols", "value": "={{ $json.symbols_string }}"}]}, "options": {"timeout": 15000, "retry": {"enabled": true, "maxRetries": 3, "retryInterval": 2000}}}, "id": "binance-enhanced", "name": "Enhanced Binance API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 80], "onError": "continueRegularOutput"}, {"parameters": {"url": "https://pro-api.coingecko.com/api/v3/simple/price", "sendQuery": true, "queryParameters": {"parameters": [{"name": "ids", "value": "={{ $('Prepare API Parameters').first().json.symbols_array.map(s => s.toLowerCase()).join(',') }}"}, {"name": "vs_currencies", "value": "usd"}, {"name": "include_market_cap", "value": "true"}, {"name": "include_24hr_vol", "value": "true"}, {"name": "include_24hr_change", "value": "true"}]}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"timeout": 15000, "retry": {"enabled": true, "maxRetries": 3, "retryInterval": 2000}}}, "id": "coingecko-pro", "name": "CoinGecko Pro API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 200], "credentials": {"httpHeaderAuth": {"id": "coingecko-pro-api", "name": "CoinGecko Pro API"}}, "onError": "continueRegularOutput"}, {"parameters": {"url": "https://api.alternative.me/fng/", "options": {"timeout": 10000, "retry": {"enabled": true, "maxRetries": 2, "retryInterval": 1000}}}, "id": "fear-greed-enhanced", "name": "Enhanced Fear & Greed", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 320], "onError": "continueRegularOutput"}, {"parameters": {"url": "https://cryptopanic.com/api/v1/posts/", "sendQuery": true, "queryParameters": {"parameters": [{"name": "auth_token", "value": "={{ $credentials.cryptopanic.api_key }}"}, {"name": "public", "value": "true"}, {"name": "kind", "value": "news"}, {"name": "currencies", "value": "={{ $('Prepare API Parameters').first().json.symbols_array.join(',') }}"}, {"name": "filter", "value": "hot"}]}, "options": {"timeout": 15000, "retry": {"enabled": true, "maxRetries": 2, "retryInterval": 3000}}}, "id": "crypto-news-pro", "name": "CryptoPanic Pro News", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 440], "credentials": {"cryptopanic": {"id": "cryptopanic-api", "name": "CryptoPanic API"}}, "onError": "continueRegularOutput"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "id": "merge-all-sources", "name": "Merge All Data Sources", "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1000, 260], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Enhanced Data Quality Assessment\nconst allData = $input.all();\nconst results = [];\n\n// Data source mapping\nconst dataSources = {\n  binance: allData.find(d => d.json && Array.isArray(d.json)) || { json: [] },\n  coingecko: allData.find(d => d.json && typeof d.json === 'object' && !Array.isArray(d.json)) || { json: {} },\n  fearGreed: allData.find(d => d.json && d.json.data) || { json: { data: [] } },\n  news: allData.find(d => d.json && d.json.results) || { json: { results: [] } }\n};\n\n// Quality scoring function\nfunction calculateQualityScore(data) {\n  let score = 0;\n  let factors = 0;\n  \n  // Binance data quality\n  if (dataSources.binance.json.length > 0) {\n    score += 0.3;\n    factors++;\n    \n    // Check for complete data\n    const completeData = dataSources.binance.json.filter(item => \n      item.symbol && item.lastPrice && item.volume && item.priceChangePercent\n    );\n    if (completeData.length === dataSources.binance.json.length) {\n      score += 0.1;\n    }\n  }\n  \n  // CoinGecko data quality\n  if (Object.keys(dataSources.coingecko.json).length > 0) {\n    score += 0.25;\n    factors++;\n  }\n  \n  // Fear & Greed data quality\n  if (dataSources.fearGreed.json.data && dataSources.fearGreed.json.data.length > 0) {\n    score += 0.15;\n    factors++;\n  }\n  \n  // News data quality\n  if (dataSources.news.json.results && dataSources.news.json.results.length > 0) {\n    score += 0.2;\n    factors++;\n    \n    // Bonus for recent news\n    const recentNews = dataSources.news.json.results.filter(news => {\n      const newsTime = new Date(news.published_at);\n      const now = new Date();\n      return (now - newsTime) < 3600000; // 1 hour\n    });\n    if (recentNews.length > 0) {\n      score += 0.1;\n    }\n  }\n  \n  return Math.min(score, 1.0);\n}\n\n// Anomaly detection\nfunction detectAnomalies(data) {\n  const anomalies = [];\n  \n  // Price anomaly detection\n  if (dataSources.binance.json.length > 0) {\n    dataSources.binance.json.forEach(item => {\n      const priceChange = Math.abs(parseFloat(item.priceChangePercent));\n      if (priceChange > 20) {\n        anomalies.push({\n          type: 'EXTREME_PRICE_CHANGE',\n          symbol: item.symbol,\n          change: priceChange,\n          severity: priceChange > 50 ? 'CRITICAL' : 'HIGH'\n        });\n      }\n      \n      // Volume anomaly\n      const volume = parseFloat(item.volume);\n      const quoteVolume = parseFloat(item.quoteVolume);\n      if (volume > 0 && quoteVolume / volume > 1000000) {\n        anomalies.push({\n          type: 'UNUSUAL_VOLUME',\n          symbol: item.symbol,\n          volume_ratio: quoteVolume / volume,\n          severity: 'MEDIUM'\n        });\n      }\n    });\n  }\n  \n  return anomalies;\n}\n\n// Market signals extraction\nfunction extractMarketSignals(data) {\n  const signals = [];\n  \n  // Fear & Greed signals\n  if (dataSources.fearGreed.json.data && dataSources.fearGreed.json.data[0]) {\n    const fgValue = parseInt(dataSources.fearGreed.json.data[0].value);\n    if (fgValue <= 20) {\n      signals.push({\n        type: 'EXTREME_FEAR',\n        value: fgValue,\n        interpretation: 'POTENTIAL_BUY_OPPORTUNITY',\n        confidence: 0.8\n      });\n    } else if (fgValue >= 80) {\n      signals.push({\n        type: 'EXTREME_GREED',\n        value: fgValue,\n        interpretation: 'POTENTIAL_SELL_SIGNAL',\n        confidence: 0.8\n      });\n    }\n  }\n  \n  // News sentiment signals\n  if (dataSources.news.json.results && dataSources.news.json.results.length > 0) {\n    const positiveNews = dataSources.news.json.results.filter(news => \n      news.kind === 'news' && (news.title.toLowerCase().includes('bullish') || \n      news.title.toLowerCase().includes('positive') || news.title.toLowerCase().includes('surge'))\n    );\n    \n    const negativeNews = dataSources.news.json.results.filter(news => \n      news.kind === 'news' && (news.title.toLowerCase().includes('bearish') || \n      news.title.toLowerCase().includes('crash') || news.title.toLowerCase().includes('dump'))\n    );\n    \n    if (positiveNews.length > negativeNews.length * 2) {\n      signals.push({\n        type: 'POSITIVE_NEWS_SENTIMENT',\n        positive_count: positiveNews.length,\n        negative_count: negativeNews.length,\n        interpretation: 'BULLISH_SENTIMENT',\n        confidence: 0.6\n      });\n    } else if (negativeNews.length > positiveNews.length * 2) {\n      signals.push({\n        type: 'NEGATIVE_NEWS_SENTIMENT',\n        positive_count: positiveNews.length,\n        negative_count: negativeNews.length,\n        interpretation: 'BEARISH_SENTIMENT',\n        confidence: 0.6\n      });\n    }\n  }\n  \n  return signals;\n}\n\n// Generate comprehensive quality report\nconst qualityScore = calculateQualityScore(allData);\nconst anomalies = detectAnomalies(allData);\nconst marketSignals = extractMarketSignals(allData);\n\nconst qualityReport = {\n  timestamp: new Date().toISOString(),\n  data_quality_score: Math.round(qualityScore * 100) / 100,\n  data_sources: {\n    binance: {\n      available: dataSources.binance.json.length > 0,\n      count: dataSources.binance.json.length,\n      status: dataSources.binance.json.length > 0 ? 'ACTIVE' : 'FAILED'\n    },\n    coingecko: {\n      available: Object.keys(dataSources.coingecko.json).length > 0,\n      count: Object.keys(dataSources.coingecko.json).length,\n      status: Object.keys(dataSources.coingecko.json).length > 0 ? 'ACTIVE' : 'FAILED'\n    },\n    fear_greed: {\n      available: dataSources.fearGreed.json.data && dataSources.fearGreed.json.data.length > 0,\n      value: dataSources.fearGreed.json.data && dataSources.fearGreed.json.data[0] ? dataSources.fearGreed.json.data[0].value : null,\n      status: dataSources.fearGreed.json.data && dataSources.fearGreed.json.data.length > 0 ? 'ACTIVE' : 'FAILED'\n    },\n    news: {\n      available: dataSources.news.json.results && dataSources.news.json.results.length > 0,\n      count: dataSources.news.json.results ? dataSources.news.json.results.length : 0,\n      status: dataSources.news.json.results && dataSources.news.json.results.length > 0 ? 'ACTIVE' : 'FAILED'\n    }\n  },\n  anomalies_detected: anomalies,\n  market_signals: marketSignals,\n  data_reliability: qualityScore >= 0.8 ? 'HIGH' : qualityScore >= 0.6 ? 'MEDIUM' : 'LOW',\n  weight_factor: qualityScore >= 0.8 ? 1.0 : qualityScore >= 0.6 ? 0.8 : 0.5,\n  recommendations: [\n    qualityScore < 0.6 ? 'INCREASE_DATA_SOURCE_MONITORING' : null,\n    anomalies.length > 0 ? 'INVESTIGATE_ANOMALIES' : null,\n    marketSignals.length > 0 ? 'ANALYZE_MARKET_SIGNALS' : null\n  ].filter(r => r !== null),\n  raw_data: {\n    binance: dataSources.binance.json,\n    coingecko: dataSources.coingecko.json,\n    fear_greed: dataSources.fearGreed.json,\n    news: dataSources.news.json\n  }\n};\n\nreturn [{ json: qualityReport }];"}, "id": "enhanced-quality-assessment", "name": "Enhanced Data Quality Assessment", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 260], "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "quality-check", "leftValue": "={{ $json.data_quality_score }}", "rightValue": 0.5, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "quality-gate", "name": "Quality Gate", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1400, 260], "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO market_data_enhanced (symbol, price, volume_24h, price_change_percentage_24h, market_cap, source, timestamp, ai_quality_score, ai_analysis, data_reliability, weight_factor, anomalies_detected, market_signals) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, $7, $8, $9, $10, $11, $12) ON CONFLICT (symbol, source, DATE_TRUNC('minute', timestamp)) DO UPDATE SET price = EXCLUDED.price, ai_quality_score = EXCLUDED.ai_quality_score, ai_analysis = EXCLUDED.ai_analysis", "parameters": {"parameters": [{"name": "symbol", "value": "MULTI_ASSET"}, {"name": "price", "value": "1"}, {"name": "volume_24h", "value": "1"}, {"name": "price_change_percentage_24h", "value": "1"}, {"name": "market_cap", "value": "1"}, {"name": "source", "value": "enhanced_harvester"}, {"name": "ai_quality_score", "value": "={{ $json.data_quality_score }}"}, {"name": "ai_analysis", "value": "={{ JSON.stringify($json) }}"}, {"name": "data_reliability", "value": "={{ $json.data_reliability }}"}, {"name": "weight_factor", "value": "={{ $json.weight_factor }}"}, {"name": "anomalies_detected", "value": "={{ JSON.stringify($json.anomalies_detected) }}"}, {"name": "market_signals", "value": "={{ JSON.stringify($json.market_signals) }}"}]}, "options": {}}, "id": "store-enhanced-data", "name": "Store Enhanced Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1600, 180], "credentials": {"postgres": {"id": "trading-postgres", "name": "Trading PostgreSQL"}}, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "anomaly-check", "leftValue": "={{ $json.anomalies_detected.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "or"}, "options": {}}, "id": "anomaly-detector", "name": "Anomaly Detector", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1600, 340], "onError": "continueRegularOutput"}, {"parameters": {"url": "http://n8n-main:5678/webhook/master-strategist", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"trigger_type\": \"data_anomaly_detected\",\n  \"anomalies\": $json.anomalies_detected,\n  \"market_signals\": $json.market_signals,\n  \"quality_score\": $json.data_quality_score,\n  \"timestamp\": $json.timestamp\n}) }}", "options": {"timeout": 10000}}, "id": "trigger-anomaly-alert", "name": "<PERSON><PERSON> Anomaly <PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1800, 260], "onError": "continueRegularOutput"}], "connections": {"schedule-trigger": {"main": [[{"node": "get-active-symbols", "type": "main", "index": 0}]]}, "get-active-symbols": {"main": [[{"node": "split-symbols", "type": "main", "index": 0}]]}, "split-symbols": {"main": [[{"node": "prepare-api-params", "type": "main", "index": 0}]]}, "prepare-api-params": {"main": [[{"node": "binance-enhanced", "type": "main", "index": 0}, {"node": "coingecko-pro", "type": "main", "index": 0}, {"node": "fear-greed-enhanced", "type": "main", "index": 0}, {"node": "crypto-news-pro", "type": "main", "index": 0}]]}, "binance-enhanced": {"main": [[{"node": "merge-all-sources", "type": "main", "index": 0}]]}, "coingecko-pro": {"main": [[{"node": "merge-all-sources", "type": "main", "index": 1}]]}, "fear-greed-enhanced": {"main": [[{"node": "merge-all-sources", "type": "main", "index": 2}]]}, "crypto-news-pro": {"main": [[{"node": "merge-all-sources", "type": "main", "index": 3}]]}, "merge-all-sources": {"main": [[{"node": "enhanced-quality-assessment", "type": "main", "index": 0}]]}, "enhanced-quality-assessment": {"main": [[{"node": "quality-gate", "type": "main", "index": 0}]]}, "quality-gate": {"main": [[{"node": "store-enhanced-data", "type": "main", "index": 0}, {"node": "anomaly-detector", "type": "main", "index": 0}]]}, "anomaly-detector": {"main": [[{"node": "trigger-anomaly-alert", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": {"id": "error-handler-workflow"}}, "staticData": {}, "tags": [{"createdAt": "2025-01-22T10:00:00.000Z", "updatedAt": "2025-01-22T10:00:00.000Z", "id": "enhanced-data-harvesting", "name": "Enhanced Data Harvesting"}], "triggerCount": 1, "updatedAt": "2025-01-22T10:00:00.000Z", "versionId": "2.0.0"}