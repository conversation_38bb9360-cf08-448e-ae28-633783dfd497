{"name": "💬 Telegram Herald Assistant", "nodes": [{"parameters": {"httpMethod": "POST", "path": "telegram-herald", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Strategy Alerts Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [48, 176], "webhookId": "664e05fc-d732-4931-8d74-b3ff6b6f9f07", "onError": "continueRegularOutput"}, {"parameters": {"additionalFields": {}}, "id": "telegram-trigger", "name": "Telegram Commands", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-96, 416], "webhookId": "06a95173-01dc-4298-86d1-19219269b1c3", "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "Sen çılgın bir kripto trading asistanısın! Kullanıcıya proaktif ve eğlenceli şekilde yardım ediyorsun.\n\nGelen veri:\n{{JSON.stringify($json, null, 2)}}\n\nGörevlerin:\n1. Veriyi analiz et ve kullanıcı dostu hale getir\n2. Heyecan verici ve ilgi çekici mesaj oluştur\n3. Emojiler ve Türkçe kullan\n4. Eyleme dönüştürülebilir öneriler sun\n5. Kullanıcıyı motive et\n\nJSON formatında telegram mesajı hazırla.", "hasOutputParser": true, "options": {}}, "id": "ai-herald-agent", "name": "AI Herald Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [256, 256], "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "const data = $input.all()[0].json;\n\n// Mesaj formatını oluştur\nlet message = `${data.emoji_mood || '🚀'} **${data.title || 'Kripto Güncelleme'}** ${data.emoji_mood || '🚀'}\\n\\n`;\nmessage += `${data.main_message || 'Yeni bir güncelleme mevcut!'}\\n\\n`;\n\nif (data.action_buttons && data.action_buttons.length > 0) {\n  message += `🎯 **Seçenekler:**\\n`;\n  data.action_buttons.forEach((button, index) => {\n    message += `${index + 1}. ${button}\\n`;\n  });\n  message += `\\n`;\n}\n\nif (data.follow_up_suggestion) {\n  message += `💡 **Öneri:** ${data.follow_up_suggestion}\\n\\n`;\n}\n\nmessage += `🤖 *Çılgın AI Trading Asistanı*`;\n\nreturn [{\n  json: {\n    formatted_message: message,\n    urgency: data.urgency_level || 'NORMAL',\n    message_type: data.message_type || 'INFO'\n  }\n}];"}, "id": "format-telegram-message", "name": "Format Telegram Message", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [656, 288], "onError": "continueRegularOutput"}, {"parameters": {"chatId": "={{ $credentials.telegram_bot.chat_id }}", "text": "={{ $json.formatted_message }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "id": "send-telegram-message", "name": "Send Telegram Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [832, 400], "webhookId": "dc3ce264-dbbb-496d-b46f-cc708066ccf7", "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM actionable_insights WHERE timestamp > NOW() - INTERVAL '1 hour' ORDER BY timestamp DESC LIMIT 5", "options": {}}, "id": "get-latest-insights", "name": "Get Latest Insights", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [176, 464], "retryOnFail": true, "maxTries": 3, "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "Sen kullanıcı komutlarını işleyen çılgın asistansın. Kullanıcı mesajı: {{ $('Telegram Commands').first().json.message?.text || 'Bilinmeyen komut' }}\n\nMevcut veriler:\n{{JSON.stringify($json, null, 2)}}\n\nKomutlar:\n/status - Genel durum\n/analiz - Detaylı analiz\n/risk - Risk durumu\n/strateji - Son stratejiler\n/help - <PERSON><PERSON><PERSON>ya uygun yanıt hazırla.", "hasOutputParser": true, "options": {}}, "id": "ai-command-handler", "name": "AI Command Handler", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [400, 496], "onError": "continueRegularOutput"}, {"parameters": {"options": {}}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.5, "position": [1120, 336], "onError": "continueRegularOutput"}, {"parameters": {"additionalFields": {}}, "id": "callback-handler", "name": "<PERSON><PERSON> Callback Queries", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [288, 592], "webhookId": "348f4a99-e77d-4e98-b354-15f4310697dd", "onError": "continueRegularOutput"}], "pinData": {}, "connections": {"Strategy Alerts Webhook": {"main": [[{"node": "AI Herald Agent", "type": "main", "index": 0}]]}, "Telegram Commands": {"main": [[{"node": "Get Latest Insights", "type": "main", "index": 0}]]}, "AI Herald Agent": {"main": [[{"node": "Format Telegram Message", "type": "main", "index": 0}]]}, "Format Telegram Message": {"main": [[{"node": "Send Telegram Message", "type": "main", "index": 0}]]}, "Send Telegram Message": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Get Latest Insights": {"main": [[{"node": "AI Command Handler", "type": "main", "index": 0}]]}, "AI Command Handler": {"main": [[{"node": "Send Telegram Message", "type": "main", "index": 0}]]}, "Handle Callback Queries": {"main": [[{"node": "AI Command Handler", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": true, "saveExecutionProgress": true}, "versionId": "9acaa494-bce1-486c-a286-f3b33545c563", "meta": {"instanceId": "a714df1aa11da563210a0570d83019f8d8c6597fc9f1fe509b3525f7215ea45e"}, "id": "MTKpOGIIshG8kL5l", "tags": []}